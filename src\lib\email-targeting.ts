import { db } from '@/lib/db';
import { users, usermeta, userEmailPreferences, USER_STATUS } from '@/lib/db/schema';
import { eq, and, inArray, like, or, sql } from 'drizzle-orm';
import { canEmailReceiveEmail } from '@/lib/email-preferences';

export interface EmailRecipient {
  id: number;
  email: string;
  displayName: string;
  username: string;
  role: string;
  canReceiveCampaigns: boolean;
}

export interface RecipientQuery {
  type: 'all_users' | 'role_based' | 'individual_users' | 'custom_group';
  roles?: string[];
  userIds?: number[];
  includeInactive?: boolean;
  emailType?: 'campaigns' | 'notifications' | 'post_updates' | 'account_updates';
}

export interface RecipientStats {
  totalUsers: number;
  activeUsers: number;
  usersByRole: Record<string, number>;
  usersWithEmailPreferences: number;
  usersOptedOut: number;
}

// Get users by role
export async function getUsersByRole(roles: string[], includeInactive = false): Promise<EmailRecipient[]> {
  // Build the capabilities query for role matching
  const roleCapabilities = roles.map(role => {
    switch (role.toUpperCase()) {
      case 'ADMIN':
        return '%administrator%';
      case 'EDITOR':
        return '%editor%';
      case 'AUTHOR':
        return '%author%';
      default:
        return `%${role.toLowerCase()}%`;
    }
  });

  const statusCondition = includeInactive 
    ? undefined 
    : eq(users.user_status, USER_STATUS.APPROVED);

  const result = await db
    .select({
      id: users.ID,
      email: users.user_email,
      displayName: users.display_name,
      username: users.user_login,
      role: usermeta.meta_value,
      // Email preferences
      email_campaigns: userEmailPreferences.email_campaigns,
    })
    .from(users)
    .innerJoin(
      usermeta, 
      and(
        eq(usermeta.user_id, users.ID),
        eq(usermeta.meta_key, 'wikify1h_capabilities')
      )
    )
    .leftJoin(userEmailPreferences, eq(userEmailPreferences.user_id, users.ID))
    .where(
      and(
        statusCondition,
        or(...roleCapabilities.map(cap => like(usermeta.meta_value, cap)))
      )
    );

  return result.map(user => ({
    id: user.id,
    email: user.email,
    displayName: user.displayName || user.username,
    username: user.username,
    role: extractRoleFromCapabilities(user.role || ''),
    canReceiveCampaigns: user.email_campaigns !== false, // Default to true if no preferences
  }));
}

// Get all active users
export async function getAllUsers(includeInactive = false): Promise<EmailRecipient[]> {
  const statusCondition = includeInactive 
    ? undefined 
    : eq(users.user_status, USER_STATUS.APPROVED);

  const result = await db
    .select({
      id: users.ID,
      email: users.user_email,
      displayName: users.display_name,
      username: users.user_login,
      role: usermeta.meta_value,
      // Email preferences
      email_campaigns: userEmailPreferences.email_campaigns,
    })
    .from(users)
    .leftJoin(
      usermeta, 
      and(
        eq(usermeta.user_id, users.ID),
        eq(usermeta.meta_key, 'wikify1h_capabilities')
      )
    )
    .leftJoin(userEmailPreferences, eq(userEmailPreferences.user_id, users.ID))
    .where(statusCondition);

  return result.map(user => ({
    id: user.id,
    email: user.email,
    displayName: user.displayName || user.username,
    username: user.username,
    role: extractRoleFromCapabilities(user.role || ''),
    canReceiveCampaigns: user.email_campaigns !== false, // Default to true if no preferences
  }));
}

// Get users by specific IDs
export async function getUsersByIds(userIds: number[]): Promise<EmailRecipient[]> {
  if (userIds.length === 0) return [];

  const result = await db
    .select({
      id: users.ID,
      email: users.user_email,
      displayName: users.display_name,
      username: users.user_login,
      role: usermeta.meta_value,
      status: users.user_status,
      // Email preferences
      email_campaigns: userEmailPreferences.email_campaigns,
    })
    .from(users)
    .leftJoin(
      usermeta, 
      and(
        eq(usermeta.user_id, users.ID),
        eq(usermeta.meta_key, 'wikify1h_capabilities')
      )
    )
    .leftJoin(userEmailPreferences, eq(userEmailPreferences.user_id, users.ID))
    .where(inArray(users.ID, userIds));

  return result.map(user => ({
    id: user.id,
    email: user.email,
    displayName: user.displayName || user.username,
    username: user.username,
    role: extractRoleFromCapabilities(user.role || ''),
    canReceiveCampaigns: user.email_campaigns !== false, // Default to true if no preferences
  }));
}

// Get recipients based on query parameters
export async function getEmailRecipients(query: RecipientQuery): Promise<EmailRecipient[]> {
  let recipients: EmailRecipient[] = [];

  switch (query.type) {
    case 'all_users':
      recipients = await getAllUsers(query.includeInactive);
      break;
    
    case 'role_based':
      if (!query.roles || query.roles.length === 0) {
        throw new Error('Roles must be specified for role-based targeting');
      }
      recipients = await getUsersByRole(query.roles, query.includeInactive);
      break;
    
    case 'individual_users':
      if (!query.userIds || query.userIds.length === 0) {
        throw new Error('User IDs must be specified for individual user targeting');
      }
      recipients = await getUsersByIds(query.userIds);
      break;
    
    case 'custom_group':
      // For now, custom groups are not implemented
      // This could be extended to support saved user groups
      throw new Error('Custom groups are not yet implemented');
    
    default:
      throw new Error('Invalid recipient type');
  }

  // Filter by email preferences if specified
  if (query.emailType) {
    const filteredRecipients: EmailRecipient[] = [];
    
    for (const recipient of recipients) {
      const canReceive = await canEmailReceiveEmail(recipient.email, query.emailType);
      if (canReceive) {
        filteredRecipients.push({
          ...recipient,
          canReceiveCampaigns: canReceive,
        });
      }
    }
    
    recipients = filteredRecipients;
  }

  return recipients;
}

// Get recipient statistics
export async function getRecipientStats(): Promise<RecipientStats> {
  // Get total users
  const totalUsersResult = await db
    .select({ count: sql<number>`count(*)` })
    .from(users);
  
  const totalUsers = totalUsersResult[0]?.count || 0;

  // Get active users
  const activeUsersResult = await db
    .select({ count: sql<number>`count(*)` })
    .from(users)
    .where(eq(users.user_status, USER_STATUS.APPROVED));
  
  const activeUsers = activeUsersResult[0]?.count || 0;

  // Get users by role
  const roleStatsResult = await db
    .select({
      role: usermeta.meta_value,
      count: sql<number>`count(*)`
    })
    .from(users)
    .innerJoin(
      usermeta, 
      and(
        eq(usermeta.user_id, users.ID),
        eq(usermeta.meta_key, 'wikify1h_capabilities')
      )
    )
    .where(eq(users.user_status, USER_STATUS.APPROVED))
    .groupBy(usermeta.meta_value);

  const usersByRole: Record<string, number> = {};
  roleStatsResult.forEach(stat => {
    const role = extractRoleFromCapabilities(stat.role || '');
    usersByRole[role] = (usersByRole[role] || 0) + stat.count;
  });

  // Get users with email preferences
  const usersWithPrefsResult = await db
    .select({ count: sql<number>`count(*)` })
    .from(userEmailPreferences);
  
  const usersWithEmailPreferences = usersWithPrefsResult[0]?.count || 0;

  // Get users opted out of campaigns
  const usersOptedOutResult = await db
    .select({ count: sql<number>`count(*)` })
    .from(userEmailPreferences)
    .where(eq(userEmailPreferences.email_campaigns, false));
  
  const usersOptedOut = usersOptedOutResult[0]?.count || 0;

  return {
    totalUsers,
    activeUsers,
    usersByRole,
    usersWithEmailPreferences,
    usersOptedOut,
  };
}

// Search users for recipient selection
export async function searchUsers(query: string, limit = 20): Promise<EmailRecipient[]> {
  const searchTerm = `%${query}%`;
  
  const result = await db
    .select({
      id: users.ID,
      email: users.user_email,
      displayName: users.display_name,
      username: users.user_login,
      role: usermeta.meta_value,
      status: users.user_status,
      // Email preferences
      email_campaigns: userEmailPreferences.email_campaigns,
    })
    .from(users)
    .leftJoin(
      usermeta, 
      and(
        eq(usermeta.user_id, users.ID),
        eq(usermeta.meta_key, 'wikify1h_capabilities')
      )
    )
    .leftJoin(userEmailPreferences, eq(userEmailPreferences.user_id, users.ID))
    .where(
      and(
        eq(users.user_status, USER_STATUS.APPROVED),
        or(
          like(users.user_login, searchTerm),
          like(users.user_email, searchTerm),
          like(users.display_name, searchTerm)
        )
      )
    )
    .limit(limit);

  return result.map(user => ({
    id: user.id,
    email: user.email,
    displayName: user.displayName || user.username,
    username: user.username,
    role: extractRoleFromCapabilities(user.role || ''),
    canReceiveCampaigns: user.email_campaigns !== false, // Default to true if no preferences
  }));
}

// Helper function to extract role from WordPress capabilities string
function extractRoleFromCapabilities(capabilities: string): string {
  if (!capabilities) return 'AUTHOR';
  
  if (capabilities.includes('administrator')) return 'ADMIN';
  if (capabilities.includes('editor')) return 'EDITOR';
  if (capabilities.includes('author')) return 'AUTHOR';
  
  return 'AUTHOR';
}

// Validate recipient query
export function validateRecipientQuery(query: RecipientQuery): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!query.type) {
    errors.push('Recipient type is required');
  }

  if (query.type === 'role_based' && (!query.roles || query.roles.length === 0)) {
    errors.push('At least one role must be specified for role-based targeting');
  }

  if (query.type === 'individual_users' && (!query.userIds || query.userIds.length === 0)) {
    errors.push('At least one user ID must be specified for individual user targeting');
  }

  if (query.roles) {
    const validRoles = ['ADMIN', 'EDITOR', 'AUTHOR'];
    const invalidRoles = query.roles.filter(role => !validRoles.includes(role.toUpperCase()));
    if (invalidRoles.length > 0) {
      errors.push(`Invalid roles: ${invalidRoles.join(', ')}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
