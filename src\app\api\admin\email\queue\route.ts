import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getQueueStats } from '@/lib/email-queue';
import { db } from '@/lib/db';
import { emailQueue, emailCampaigns } from '@/lib/db/schema';
import { eq, desc, and, gte } from 'drizzle-orm';

// GET /api/admin/email/queue - Get email queue status and recent items
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');

    // Get queue statistics
    const stats = await getQueueStats();

    // Get recent queue items
    let whereConditions = [];
    
    if (status) {
      whereConditions.push(eq(emailQueue.status, status as any));
    }

    // Only show items from the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    whereConditions.push(gte(emailQueue.created_at, sevenDaysAgo));

    const whereClause = whereConditions.length > 0 
      ? whereConditions.reduce((acc, condition) => acc && condition)
      : undefined;

    const recentItemsQuery = db
      .select({
        id: emailQueue.id,
        campaign_id: emailQueue.campaign_id,
        recipient_email: emailQueue.recipient_email,
        recipient_name: emailQueue.recipient_name,
        subject: emailQueue.subject,
        status: emailQueue.status,
        attempts: emailQueue.attempts,
        max_attempts: emailQueue.max_attempts,
        error_message: emailQueue.error_message,
        scheduled_at: emailQueue.scheduled_at,
        sent_at: emailQueue.sent_at,
        created_at: emailQueue.created_at,
        campaign_name: emailCampaigns.name,
      })
      .from(emailQueue)
      .leftJoin(emailCampaigns, eq(emailQueue.campaign_id, emailCampaigns.id))
      .orderBy(desc(emailQueue.created_at))
      .limit(limit);

    if (whereClause) {
      recentItemsQuery.where(whereClause);
    }

    const recentItems = await recentItemsQuery;

    // Calculate processing rate (emails per hour in the last 24 hours)
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

    const sentLast24Hours = await db
      .select({ count: emailQueue.id })
      .from(emailQueue)
      .where(
        and(
          eq(emailQueue.status, 'sent'),
          gte(emailQueue.sent_at!, twentyFourHoursAgo)
        )
      );

    const processingRate = sentLast24Hours.length;

    return NextResponse.json({
      success: true,
      data: {
        stats,
        recentItems: recentItems.map(item => ({
          id: item.id,
          campaignId: item.campaign_id,
          campaignName: item.campaign_name,
          recipientEmail: item.recipient_email,
          recipientName: item.recipient_name,
          subject: item.subject,
          status: item.status,
          attempts: item.attempts,
          maxAttempts: item.max_attempts,
          errorMessage: item.error_message,
          scheduledAt: item.scheduled_at,
          sentAt: item.sent_at,
          createdAt: item.created_at,
        })),
        processingRate: {
          emailsLast24Hours: processingRate,
          averagePerHour: Math.round(processingRate / 24),
        },
      },
    });

  } catch (error) {
    console.error('Error fetching email queue:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch email queue' },
      { status: 500 }
    );
  }
}
