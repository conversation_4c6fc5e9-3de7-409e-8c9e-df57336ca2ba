import { db } from '@/lib/db';
import { userEmailPreferences, users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import crypto from 'crypto';

export interface EmailPreferences {
  id: number;
  userId: number;
  emailCampaigns: boolean;
  emailNotifications: boolean;
  postUpdates: boolean;
  accountUpdates: boolean;
  unsubscribeToken: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface UpdatePreferencesParams {
  userId: number;
  emailCampaigns?: boolean;
  emailNotifications?: boolean;
  postUpdates?: boolean;
  accountUpdates?: boolean;
}

// Generate a unique unsubscribe token
function generateUnsubscribeToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Create default email preferences for a new user
export async function createDefaultEmailPreferences(userId: number): Promise<void> {
  const now = new Date();
  const unsubscribeToken = generateUnsubscribeToken();

  try {
    await db.insert(userEmailPreferences).values({
      user_id: userId,
      email_campaigns: true,
      email_notifications: true,
      post_updates: true,
      account_updates: true,
      unsubscribe_token: unsubscribeToken,
      created_at: now,
      updated_at: now,
    });
  } catch (error) {
    // If preferences already exist, ignore the error
    console.log(`Email preferences already exist for user ${userId}`);
  }
}

// Get user email preferences
export async function getUserEmailPreferences(userId: number): Promise<EmailPreferences | null> {
  const result = await db
    .select()
    .from(userEmailPreferences)
    .where(eq(userEmailPreferences.user_id, userId))
    .limit(1);

  if (result.length === 0) {
    // Create default preferences if they don't exist
    await createDefaultEmailPreferences(userId);
    return getUserEmailPreferences(userId);
  }

  const prefs = result[0];
  return {
    id: prefs.id,
    userId: prefs.user_id,
    emailCampaigns: prefs.email_campaigns,
    emailNotifications: prefs.email_notifications,
    postUpdates: prefs.post_updates,
    accountUpdates: prefs.account_updates,
    unsubscribeToken: prefs.unsubscribe_token,
    createdAt: prefs.created_at,
    updatedAt: prefs.updated_at,
  };
}

// Update user email preferences
export async function updateUserEmailPreferences(params: UpdatePreferencesParams): Promise<void> {
  const now = new Date();
  
  // Ensure preferences exist first
  await getUserEmailPreferences(params.userId);

  const updateData: any = {
    updated_at: now,
  };

  if (params.emailCampaigns !== undefined) updateData.email_campaigns = params.emailCampaigns;
  if (params.emailNotifications !== undefined) updateData.email_notifications = params.emailNotifications;
  if (params.postUpdates !== undefined) updateData.post_updates = params.postUpdates;
  if (params.accountUpdates !== undefined) updateData.account_updates = params.accountUpdates;

  await db
    .update(userEmailPreferences)
    .set(updateData)
    .where(eq(userEmailPreferences.user_id, params.userId));
}

// Get user preferences by email address
export async function getUserPreferencesByEmail(email: string): Promise<EmailPreferences | null> {
  const result = await db
    .select({
      id: userEmailPreferences.id,
      user_id: userEmailPreferences.user_id,
      email_campaigns: userEmailPreferences.email_campaigns,
      email_notifications: userEmailPreferences.email_notifications,
      post_updates: userEmailPreferences.post_updates,
      account_updates: userEmailPreferences.account_updates,
      unsubscribe_token: userEmailPreferences.unsubscribe_token,
      created_at: userEmailPreferences.created_at,
      updated_at: userEmailPreferences.updated_at,
    })
    .from(userEmailPreferences)
    .innerJoin(users, eq(userEmailPreferences.user_id, users.ID))
    .where(eq(users.user_email, email))
    .limit(1);

  if (result.length === 0) return null;

  const prefs = result[0];
  return {
    id: prefs.id,
    userId: prefs.user_id,
    emailCampaigns: prefs.email_campaigns,
    emailNotifications: prefs.email_notifications,
    postUpdates: prefs.post_updates,
    accountUpdates: prefs.account_updates,
    unsubscribeToken: prefs.unsubscribe_token,
    createdAt: prefs.created_at,
    updatedAt: prefs.updated_at,
  };
}

// Get user preferences by unsubscribe token
export async function getUserPreferencesByToken(token: string): Promise<{
  preferences: EmailPreferences;
  user: {
    id: number;
    email: string;
    displayName: string;
  };
} | null> {
  const result = await db
    .select({
      // Preferences
      pref_id: userEmailPreferences.id,
      user_id: userEmailPreferences.user_id,
      email_campaigns: userEmailPreferences.email_campaigns,
      email_notifications: userEmailPreferences.email_notifications,
      post_updates: userEmailPreferences.post_updates,
      account_updates: userEmailPreferences.account_updates,
      unsubscribe_token: userEmailPreferences.unsubscribe_token,
      pref_created_at: userEmailPreferences.created_at,
      pref_updated_at: userEmailPreferences.updated_at,
      // User info
      user_email: users.user_email,
      display_name: users.display_name,
    })
    .from(userEmailPreferences)
    .innerJoin(users, eq(userEmailPreferences.user_id, users.ID))
    .where(eq(userEmailPreferences.unsubscribe_token, token))
    .limit(1);

  if (result.length === 0) return null;

  const data = result[0];
  return {
    preferences: {
      id: data.pref_id,
      userId: data.user_id,
      emailCampaigns: data.email_campaigns,
      emailNotifications: data.email_notifications,
      postUpdates: data.post_updates,
      accountUpdates: data.account_updates,
      unsubscribeToken: data.unsubscribe_token,
      createdAt: data.pref_created_at,
      updatedAt: data.pref_updated_at,
    },
    user: {
      id: data.user_id,
      email: data.user_email,
      displayName: data.display_name,
    },
  };
}

// Unsubscribe user from all emails using token
export async function unsubscribeUserByToken(token: string): Promise<boolean> {
  const result = await db
    .update(userEmailPreferences)
    .set({
      email_campaigns: false,
      email_notifications: false,
      post_updates: false,
      account_updates: false,
      updated_at: new Date(),
    })
    .where(eq(userEmailPreferences.unsubscribe_token, token));

  return result.affectedRows > 0;
}

// Unsubscribe user from specific email type using token
export async function unsubscribeFromEmailType(
  token: string, 
  emailType: 'campaigns' | 'notifications' | 'post_updates' | 'account_updates'
): Promise<boolean> {
  const updateData: any = {
    updated_at: new Date(),
  };

  switch (emailType) {
    case 'campaigns':
      updateData.email_campaigns = false;
      break;
    case 'notifications':
      updateData.email_notifications = false;
      break;
    case 'post_updates':
      updateData.post_updates = false;
      break;
    case 'account_updates':
      updateData.account_updates = false;
      break;
  }

  const result = await db
    .update(userEmailPreferences)
    .set(updateData)
    .where(eq(userEmailPreferences.unsubscribe_token, token));

  return result.affectedRows > 0;
}

// Check if user can receive specific email type
export async function canUserReceiveEmail(
  userId: number, 
  emailType: 'campaigns' | 'notifications' | 'post_updates' | 'account_updates'
): Promise<boolean> {
  const preferences = await getUserEmailPreferences(userId);
  if (!preferences) return false;

  switch (emailType) {
    case 'campaigns':
      return preferences.emailCampaigns;
    case 'notifications':
      return preferences.emailNotifications;
    case 'post_updates':
      return preferences.postUpdates;
    case 'account_updates':
      return preferences.accountUpdates;
    default:
      return false;
  }
}

// Check if user can receive email by email address
export async function canEmailReceiveEmail(
  email: string, 
  emailType: 'campaigns' | 'notifications' | 'post_updates' | 'account_updates'
): Promise<boolean> {
  const preferences = await getUserPreferencesByEmail(email);
  if (!preferences) return true; // Default to true if no preferences found

  switch (emailType) {
    case 'campaigns':
      return preferences.emailCampaigns;
    case 'notifications':
      return preferences.emailNotifications;
    case 'post_updates':
      return preferences.postUpdates;
    case 'account_updates':
      return preferences.accountUpdates;
    default:
      return false;
  }
}

// Get unsubscribe statistics
export async function getUnsubscribeStats(): Promise<{
  totalUsers: number;
  campaignsOptOut: number;
  notificationsOptOut: number;
  postUpdatesOptOut: number;
  accountUpdatesOptOut: number;
}> {
  const stats = await db
    .select({
      total: userEmailPreferences.id,
      campaigns_opt_out: userEmailPreferences.email_campaigns,
      notifications_opt_out: userEmailPreferences.email_notifications,
      post_updates_opt_out: userEmailPreferences.post_updates,
      account_updates_opt_out: userEmailPreferences.account_updates,
    })
    .from(userEmailPreferences);

  const totalUsers = stats.length;
  const campaignsOptOut = stats.filter(s => !s.campaigns_opt_out).length;
  const notificationsOptOut = stats.filter(s => !s.notifications_opt_out).length;
  const postUpdatesOptOut = stats.filter(s => !s.post_updates_opt_out).length;
  const accountUpdatesOptOut = stats.filter(s => !s.account_updates_opt_out).length;

  return {
    totalUsers,
    campaignsOptOut,
    notificationsOptOut,
    postUpdatesOptOut,
    accountUpdatesOptOut,
  };
}

// Regenerate unsubscribe token for a user
export async function regenerateUnsubscribeToken(userId: number): Promise<string> {
  const newToken = generateUnsubscribeToken();
  
  await db
    .update(userEmailPreferences)
    .set({
      unsubscribe_token: newToken,
      updated_at: new Date(),
    })
    .where(eq(userEmailPreferences.user_id, userId));

  return newToken;
}
