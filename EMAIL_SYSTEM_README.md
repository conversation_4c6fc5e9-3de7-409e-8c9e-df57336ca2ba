# Email System Documentation

## Overview

This comprehensive email system provides administrators with the ability to send targeted email campaigns to users based on roles, individual selection, or all users. The system includes proper security measures, rate limiting, and user preference management.

## Features

### 1. Role-based Email Targeting
- Send emails to all users with specific roles (ADMIN, EDITOR, AUTHOR)
- Send emails to all users in the system
- Send emails to individually selected users
- Respect user email preferences and unsubscribe settings

### 2. Email Campaign Management
- Create, edit, and manage email campaigns
- Draft campaigns for later sending
- Schedule campaigns for future delivery
- Track campaign delivery statistics

### 3. Email Templates
- Pre-built system templates
- Custom template creation
- Variable substitution ({{userName}}, {{siteName}}, etc.)
- Template preview functionality

### 4. User Email Preferences
- Individual user preference management
- Unsubscribe functionality with granular controls
- Opt-out from specific email types
- Secure unsubscribe tokens

### 5. Email Queue System
- Background processing of bulk emails
- Rate limiting to prevent server overload
- Retry logic for failed deliveries
- Delivery status tracking

### 6. Security & Rate Limiting
- Content validation to prevent malicious emails
- Rate limiting for campaign creation and sending
- Admin-only access controls
- CSRF protection and security headers

## Database Schema

The system adds the following tables:

- `email_templates` - Store email templates
- `email_campaigns` - Store campaign information
- `email_queue` - Queue for processing emails
- `user_email_preferences` - User email preferences
- `email_delivery_log` - Track email delivery events

## API Endpoints

### Campaign Management
- `GET /api/admin/email/campaigns` - List campaigns
- `POST /api/admin/email/campaigns` - Create campaign
- `GET /api/admin/email/campaigns/[id]` - Get campaign details
- `PUT /api/admin/email/campaigns/[id]` - Update campaign
- `DELETE /api/admin/email/campaigns/[id]` - Delete campaign

### Templates
- `GET /api/admin/email/templates` - List templates
- `POST /api/admin/email/templates` - Create template

### Recipients
- `GET /api/admin/email/recipients` - Get recipients by criteria
- `POST /api/admin/email/recipients/preview` - Preview recipients

### Queue Management
- `GET /api/admin/email/queue` - Get queue status
- `POST /api/cron/process-email-queue` - Process email queue (cron job)

### User Preferences
- `GET /api/unsubscribe` - Get unsubscribe page data
- `POST /api/unsubscribe` - Process unsubscribe request

## Setup Instructions

### 1. Database Migration
Run the email system table creation script:
```bash
npm run ts-node src/scripts/create-email-system-tables.ts
```

### 2. Environment Variables
Ensure these environment variables are set:
```env
# Email Configuration
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Your Site Name

# Security
CRON_SECRET=your-secret-key-for-cron-jobs
```

### 3. Email Queue Processing
Set up a cron job to process the email queue:
```bash
# Process email queue every minute
* * * * * curl -X POST -H "Authorization: Bearer your-secret-key" http://localhost:3000/api/cron/process-email-queue
```

Or use a service like Vercel Cron or similar for serverless deployments.

## Usage

### Creating an Email Campaign

1. Navigate to `/admin/email` in the admin panel
2. Click "Create Campaign"
3. Fill in campaign details:
   - Campaign name
   - Email subject
   - Email body (HTML supported)
4. Select recipients:
   - All users
   - Role-based (select specific roles)
   - Individual users (future feature)
5. Choose scheduling:
   - Send immediately
   - Schedule for later
   - Save as draft
6. Preview recipients and email content
7. Send or save the campaign

### Managing User Preferences

Users can manage their email preferences through unsubscribe links in emails. The system provides:
- Unsubscribe from all emails
- Unsubscribe from specific email types
- Granular preference management

### Template Variables

Available variables for email templates:
- `{{siteName}}` - Site name from settings
- `{{siteUrl}}` - Site URL from settings
- `{{userName}}` - Recipient's display name
- `{{userEmail}}` - Recipient's email address
- `{{currentDate}}` - Current date
- `{{currentYear}}` - Current year
- `{{unsubscribeUrl}}` - Unsubscribe URL for the user

## Rate Limits

The system implements the following rate limits:
- Campaign creation: 5 campaigns per hour per user
- Bulk email sending: 1000 emails per hour per user
- Template creation: 10 templates per hour per user
- API requests: 100 requests per minute per user
- Email preview: 50 previews per hour per user

## Security Features

- Content validation to prevent XSS and malicious content
- Rate limiting to prevent abuse
- Admin-only access to email system
- Secure unsubscribe tokens
- CSRF protection
- Security headers on all responses

## Monitoring

The system provides:
- Campaign delivery statistics
- Email queue status monitoring
- Processing rate tracking
- Failed delivery tracking
- User preference statistics

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check SMTP configuration
   - Verify email queue is being processed
   - Check rate limits

2. **High bounce rate**
   - Verify email content isn't triggering spam filters
   - Check recipient email addresses are valid
   - Review email authentication settings

3. **Slow email delivery**
   - Increase cron job frequency
   - Check rate limiting settings
   - Monitor email queue size

### Logs

Check the following for debugging:
- Application logs for email processing errors
- Email queue status via admin panel
- Campaign delivery statistics

## Future Enhancements

Potential improvements:
- Individual user selection interface
- Email analytics (open rates, click tracking)
- A/B testing for campaigns
- Advanced template editor
- Email automation workflows
- Integration with external email services
