import { db } from '@/lib/db';
import { sql } from 'drizzle-orm';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (identifier: string) => string; // Custom key generator
}

interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Clean up expired entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Clean up every minute

export async function checkRateLimit(
  identifier: string,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  const key = config.keyGenerator ? config.keyGenerator(identifier) : identifier;
  const now = Date.now();
  const windowStart = now - config.windowMs;

  // Get or create rate limit entry
  let entry = rateLimitStore.get(key);
  
  if (!entry || now > entry.resetTime) {
    // Create new entry or reset expired entry
    entry = {
      count: 0,
      resetTime: now + config.windowMs,
    };
    rateLimitStore.set(key, entry);
  }

  // Check if limit exceeded
  if (entry.count >= config.maxRequests) {
    return {
      success: false,
      limit: config.maxRequests,
      remaining: 0,
      resetTime: entry.resetTime,
      retryAfter: Math.ceil((entry.resetTime - now) / 1000),
    };
  }

  // Increment counter
  entry.count++;
  rateLimitStore.set(key, entry);

  return {
    success: true,
    limit: config.maxRequests,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.resetTime,
  };
}

// Email-specific rate limiting configurations
export const EMAIL_RATE_LIMITS = {
  // Campaign creation: 5 campaigns per hour per user
  CAMPAIGN_CREATION: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5,
    keyGenerator: (userId: string) => `campaign_creation:${userId}`,
  },

  // Bulk email sending: 1000 emails per hour per user
  BULK_EMAIL_SENDING: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 1000,
    keyGenerator: (userId: string) => `bulk_email:${userId}`,
  },

  // Template creation: 10 templates per hour per user
  TEMPLATE_CREATION: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10,
    keyGenerator: (userId: string) => `template_creation:${userId}`,
  },

  // API requests: 100 requests per minute per user
  API_REQUESTS: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    keyGenerator: (userId: string) => `api_requests:${userId}`,
  },

  // Email preview: 50 previews per hour per user
  EMAIL_PREVIEW: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 50,
    keyGenerator: (userId: string) => `email_preview:${userId}`,
  },
} as const;

// Database-based rate limiting for more persistent tracking
export async function checkDatabaseRateLimit(
  identifier: string,
  action: string,
  windowMinutes: number,
  maxRequests: number
): Promise<RateLimitResult> {
  try {
    const windowStart = new Date();
    windowStart.setMinutes(windowStart.getMinutes() - windowMinutes);

    // Create rate_limits table if it doesn't exist
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS rate_limits (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        identifier VARCHAR(255) NOT NULL,
        action VARCHAR(100) NOT NULL,
        created_at DATETIME NOT NULL,
        INDEX identifier_action_idx (identifier, action),
        INDEX created_at_idx (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Count requests in the current window
    const countResult = await db.execute(sql`
      SELECT COUNT(*) as count 
      FROM rate_limits 
      WHERE identifier = ${identifier} 
        AND action = ${action} 
        AND created_at >= ${windowStart}
    `);

    const currentCount = (countResult as any)[0]?.count || 0;

    if (currentCount >= maxRequests) {
      return {
        success: false,
        limit: maxRequests,
        remaining: 0,
        resetTime: Date.now() + (windowMinutes * 60 * 1000),
        retryAfter: windowMinutes * 60,
      };
    }

    // Record this request
    await db.execute(sql`
      INSERT INTO rate_limits (identifier, action, created_at) 
      VALUES (${identifier}, ${action}, NOW())
    `);

    // Clean up old entries (run occasionally)
    if (Math.random() < 0.01) { // 1% chance
      const cleanupTime = new Date();
      cleanupTime.setHours(cleanupTime.getHours() - 24); // Keep 24 hours of data
      
      await db.execute(sql`
        DELETE FROM rate_limits 
        WHERE created_at < ${cleanupTime}
      `);
    }

    return {
      success: true,
      limit: maxRequests,
      remaining: maxRequests - currentCount - 1,
      resetTime: Date.now() + (windowMinutes * 60 * 1000),
    };

  } catch (error) {
    console.error('Database rate limit check failed:', error);
    // Fall back to in-memory rate limiting
    return checkRateLimit(identifier, {
      windowMs: windowMinutes * 60 * 1000,
      maxRequests,
      keyGenerator: (id) => `${action}:${id}`,
    });
  }
}

// Middleware helper for rate limiting
export function createRateLimitMiddleware(config: RateLimitConfig) {
  return async (identifier: string): Promise<RateLimitResult> => {
    return checkRateLimit(identifier, config);
  };
}

// Email campaign specific rate limiting
export async function checkEmailCampaignRateLimit(
  userId: string,
  recipientCount: number
): Promise<{ allowed: boolean; reason?: string; retryAfter?: number }> {
  // Check campaign creation rate limit
  const campaignLimit = await checkRateLimit(userId, EMAIL_RATE_LIMITS.CAMPAIGN_CREATION);
  if (!campaignLimit.success) {
    return {
      allowed: false,
      reason: 'Too many campaigns created recently',
      retryAfter: campaignLimit.retryAfter,
    };
  }

  // Check bulk email rate limit
  const bulkEmailLimit = await checkRateLimit(userId, {
    ...EMAIL_RATE_LIMITS.BULK_EMAIL_SENDING,
    maxRequests: EMAIL_RATE_LIMITS.BULK_EMAIL_SENDING.maxRequests - recipientCount,
  });

  if (!bulkEmailLimit.success) {
    return {
      allowed: false,
      reason: 'Email sending rate limit exceeded',
      retryAfter: bulkEmailLimit.retryAfter,
    };
  }

  return { allowed: true };
}

// Security headers for email-related endpoints
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
} as const;

// Validate email content for security
export function validateEmailContent(content: string): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  // Check for potentially malicious content
  const dangerousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^>]*>/gi,
    /<object\b[^>]*>/gi,
    /<embed\b[^>]*>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /data:text\/html/gi,
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(content)) {
      issues.push('Content contains potentially dangerous elements');
      break;
    }
  }

  // Check content length (prevent extremely large emails)
  if (content.length > 1000000) { // 1MB limit
    issues.push('Email content is too large');
  }

  // Check for excessive external links
  const linkMatches = content.match(/https?:\/\/[^\s<>"']+/gi) || [];
  if (linkMatches.length > 50) {
    issues.push('Too many external links detected');
  }

  return {
    isValid: issues.length === 0,
    issues,
  };
}
