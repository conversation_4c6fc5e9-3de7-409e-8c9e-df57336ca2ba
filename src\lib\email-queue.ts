import { db } from '@/lib/db';
import { emailQueue, emailCampaigns, emailDeliveryLog, users, userEmailPreferences } from '@/lib/db/schema';
import { eq, and, lte, lt, inArray, sql } from 'drizzle-orm';
import { sendEmail, createEmailTransporter } from '@/lib/utils/email';
import { getSiteSettings } from '@/lib/settings';

export interface QueueEmailParams {
  campaignId?: number;
  recipientEmail: string;
  recipientName: string;
  subject: string;
  body: string;
  scheduledAt?: Date;
}

export interface EmailJobResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

// Rate limiting configuration
const RATE_LIMIT = {
  maxEmailsPerMinute: 60,
  maxEmailsPerHour: 1000,
  maxEmailsPerDay: 10000,
  batchSize: 10, // Process emails in batches
  delayBetweenBatches: 1000, // 1 second delay between batches
};

// Add email to queue
export async function queueEmail(params: QueueEmailParams): Promise<number> {
  const now = new Date();
  
  const result = await db.insert(emailQueue).values({
    campaign_id: params.campaignId || null,
    recipient_email: params.recipientEmail,
    recipient_name: params.recipientName,
    subject: params.subject,
    body: params.body,
    status: 'pending',
    attempts: 0,
    max_attempts: 3,
    scheduled_at: params.scheduledAt || now,
    created_at: now,
  });

  return result.insertId;
}

// Add multiple emails to queue (bulk operation)
export async function queueBulkEmails(emails: QueueEmailParams[]): Promise<number[]> {
  const now = new Date();
  
  const emailData = emails.map(email => ({
    campaign_id: email.campaignId || null,
    recipient_email: email.recipientEmail,
    recipient_name: email.recipientName,
    subject: email.subject,
    body: email.body,
    status: 'pending' as const,
    attempts: 0,
    max_attempts: 3,
    scheduled_at: email.scheduledAt || now,
    created_at: now,
  }));

  // Insert in batches to avoid overwhelming the database
  const batchSize = 100;
  const insertIds: number[] = [];
  
  for (let i = 0; i < emailData.length; i += batchSize) {
    const batch = emailData.slice(i, i + batchSize);
    const result = await db.insert(emailQueue).values(batch);
    
    // Generate IDs for the batch (MySQL doesn't return all IDs for bulk insert)
    const startId = result.insertId;
    for (let j = 0; j < batch.length; j++) {
      insertIds.push(startId + j);
    }
  }

  return insertIds;
}

// Process pending emails in the queue
export async function processEmailQueue(): Promise<void> {
  console.log('🚀 Starting email queue processing...');
  
  try {
    // Check rate limits before processing
    const canProcess = await checkRateLimits();
    if (!canProcess) {
      console.log('⏸️ Rate limit reached, skipping queue processing');
      return;
    }

    // Get pending emails that are scheduled to be sent
    const pendingEmails = await db
      .select()
      .from(emailQueue)
      .where(
        and(
          eq(emailQueue.status, 'pending'),
          lte(emailQueue.scheduled_at, new Date()),
          lt(emailQueue.attempts, emailQueue.max_attempts)
        )
      )
      .limit(RATE_LIMIT.batchSize)
      .orderBy(emailQueue.scheduled_at);

    if (pendingEmails.length === 0) {
      console.log('📭 No pending emails to process');
      return;
    }

    console.log(`📧 Processing ${pendingEmails.length} emails...`);

    // Process emails in batches with delay
    for (let i = 0; i < pendingEmails.length; i += RATE_LIMIT.batchSize) {
      const batch = pendingEmails.slice(i, i + RATE_LIMIT.batchSize);
      
      await Promise.all(batch.map(email => processEmail(email)));
      
      // Add delay between batches to respect rate limits
      if (i + RATE_LIMIT.batchSize < pendingEmails.length) {
        await new Promise(resolve => setTimeout(resolve, RATE_LIMIT.delayBetweenBatches));
      }
    }

    console.log('✅ Email queue processing completed');
    
  } catch (error) {
    console.error('❌ Error processing email queue:', error);
    throw error;
  }
}

// Process a single email
async function processEmail(email: typeof emailQueue.$inferSelect): Promise<void> {
  try {
    // Update status to sending
    await db
      .update(emailQueue)
      .set({ 
        status: 'sending',
        attempts: email.attempts + 1
      })
      .where(eq(emailQueue.id, email.id));

    // Check if user has unsubscribed from campaigns
    if (email.campaign_id) {
      const userPrefs = await db
        .select()
        .from(userEmailPreferences)
        .innerJoin(users, eq(users.user_email, email.recipient_email))
        .where(eq(userEmailPreferences.user_id, users.ID))
        .limit(1);

      if (userPrefs.length > 0 && !userPrefs[0].user_email_preferences.email_campaigns) {
        console.log(`📵 User ${email.recipient_email} has unsubscribed from campaigns`);
        
        await db
          .update(emailQueue)
          .set({ 
            status: 'failed',
            error_message: 'User has unsubscribed from email campaigns'
          })
          .where(eq(emailQueue.id, email.id));
        
        return;
      }
    }

    // Send the email
    const result = await sendEmail({
      to: email.recipient_email,
      subject: email.subject,
      html: email.body,
    });

    if (result) {
      // Email sent successfully
      const sentAt = new Date();
      
      await db
        .update(emailQueue)
        .set({ 
          status: 'sent',
          sent_at: sentAt
        })
        .where(eq(emailQueue.id, email.id));

      // Log successful delivery
      await logEmailDelivery({
        campaignId: email.campaign_id,
        queueId: email.id,
        recipientEmail: email.recipient_email,
        subject: email.subject,
        status: 'sent',
      });

      // Update campaign statistics
      if (email.campaign_id) {
        await db
          .update(emailCampaigns)
          .set({ 
            emails_sent: sql`emails_sent + 1`
          })
          .where(eq(emailCampaigns.id, email.campaign_id));
      }

      console.log(`✅ Email sent to ${email.recipient_email}`);
      
    } else {
      throw new Error('Email sending failed - no result returned');
    }
    
  } catch (error) {
    console.error(`❌ Failed to send email to ${email.recipient_email}:`, error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const isMaxAttempts = email.attempts + 1 >= email.max_attempts;
    
    await db
      .update(emailQueue)
      .set({ 
        status: isMaxAttempts ? 'failed' : 'pending',
        error_message: errorMessage
      })
      .where(eq(emailQueue.id, email.id));

    // Update campaign statistics for failed emails
    if (email.campaign_id && isMaxAttempts) {
      await db
        .update(emailCampaigns)
        .set({ 
          emails_failed: sql`emails_failed + 1`
        })
        .where(eq(emailCampaigns.id, email.campaign_id));
    }

    // Log failed delivery
    await logEmailDelivery({
      campaignId: email.campaign_id,
      queueId: email.id,
      recipientEmail: email.recipient_email,
      subject: email.subject,
      status: 'bounced',
      eventData: { error: errorMessage },
    });
  }
}

// Check rate limits
async function checkRateLimits(): Promise<boolean> {
  const now = new Date();
  const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  // Check emails sent in the last minute
  const emailsLastMinute = await db
    .select({ count: sql<number>`count(*)` })
    .from(emailQueue)
    .where(
      and(
        eq(emailQueue.status, 'sent'),
        lte(oneMinuteAgo, emailQueue.sent_at!)
      )
    );

  if (emailsLastMinute[0]?.count >= RATE_LIMIT.maxEmailsPerMinute) {
    return false;
  }

  // Check emails sent in the last hour
  const emailsLastHour = await db
    .select({ count: sql<number>`count(*)` })
    .from(emailQueue)
    .where(
      and(
        eq(emailQueue.status, 'sent'),
        lte(oneHourAgo, emailQueue.sent_at!)
      )
    );

  if (emailsLastHour[0]?.count >= RATE_LIMIT.maxEmailsPerHour) {
    return false;
  }

  // Check emails sent in the last day
  const emailsLastDay = await db
    .select({ count: sql<number>`count(*)` })
    .from(emailQueue)
    .where(
      and(
        eq(emailQueue.status, 'sent'),
        lte(oneDayAgo, emailQueue.sent_at!)
      )
    );

  if (emailsLastDay[0]?.count >= RATE_LIMIT.maxEmailsPerDay) {
    return false;
  }

  return true;
}

// Log email delivery events
interface LogEmailDeliveryParams {
  campaignId?: number | null;
  queueId?: number;
  recipientEmail: string;
  subject: string;
  status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained' | 'unsubscribed';
  eventData?: any;
  userAgent?: string;
  ipAddress?: string;
}

export async function logEmailDelivery(params: LogEmailDeliveryParams): Promise<void> {
  try {
    await db.insert(emailDeliveryLog).values({
      campaign_id: params.campaignId || null,
      queue_id: params.queueId || null,
      recipient_email: params.recipientEmail,
      subject: params.subject,
      status: params.status,
      event_data: params.eventData ? JSON.stringify(params.eventData) : null,
      user_agent: params.userAgent || null,
      ip_address: params.ipAddress || null,
      created_at: new Date(),
    });
  } catch (error) {
    console.error('Failed to log email delivery:', error);
    // Don't throw error as this is just logging
  }
}

// Get queue statistics
export async function getQueueStats() {
  const stats = await db
    .select({
      status: emailQueue.status,
      count: sql<number>`count(*)`
    })
    .from(emailQueue)
    .groupBy(emailQueue.status);

  return stats.reduce((acc, stat) => {
    acc[stat.status] = stat.count;
    return acc;
  }, {} as Record<string, number>);
}

// Clean up old queue entries (older than 30 days)
export async function cleanupOldQueueEntries(): Promise<void> {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  await db
    .delete(emailQueue)
    .where(
      and(
        inArray(emailQueue.status, ['sent', 'failed']),
        lt(emailQueue.created_at, thirtyDaysAgo)
      )
    );

  console.log('🧹 Cleaned up old email queue entries');
}
