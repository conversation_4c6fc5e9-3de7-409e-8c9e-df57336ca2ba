import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { emailCampaigns, users } from '@/lib/db/schema';
import { eq, desc, like, or, and, count } from 'drizzle-orm';
import { getEmailRecipients, validateRecipientQuery } from '@/lib/email-targeting';
import { processTemplateVariables } from '@/lib/email-templates';
import { queueBulkEmails } from '@/lib/email-queue';
import { checkEmailCampaignRateLimit, validateEmailContent, SECURITY_HEADERS } from '@/lib/rate-limiter';

// GET /api/admin/email/campaigns - List email campaigns
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';

    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = [];
    
    if (search) {
      whereConditions.push(
        or(
          like(emailCampaigns.name, `%${search}%`),
          like(emailCampaigns.subject, `%${search}%`)
        )
      );
    }

    if (status) {
      whereConditions.push(eq(emailCampaigns.status, status as any));
    }

    const whereClause = whereConditions.length > 0 
      ? whereConditions.reduce((acc, condition) => acc && condition)
      : undefined;

    // Get campaigns with creator info
    const campaignsQuery = db
      .select({
        id: emailCampaigns.id,
        name: emailCampaigns.name,
        subject: emailCampaigns.subject,
        recipient_type: emailCampaigns.recipient_type,
        recipient_roles: emailCampaigns.recipient_roles,
        recipient_user_ids: emailCampaigns.recipient_user_ids,
        status: emailCampaigns.status,
        scheduled_at: emailCampaigns.scheduled_at,
        sent_at: emailCampaigns.sent_at,
        total_recipients: emailCampaigns.total_recipients,
        emails_sent: emailCampaigns.emails_sent,
        emails_failed: emailCampaigns.emails_failed,
        created_by: emailCampaigns.created_by,
        created_at: emailCampaigns.created_at,
        updated_at: emailCampaigns.updated_at,
        creator_name: users.display_name,
        creator_username: users.user_login,
      })
      .from(emailCampaigns)
      .leftJoin(users, eq(emailCampaigns.created_by, users.ID))
      .orderBy(desc(emailCampaigns.created_at))
      .limit(limit)
      .offset(offset);

    if (whereClause) {
      campaignsQuery.where(whereClause);
    }

    const campaigns = await campaignsQuery;

    // Get total count
    const countQuery = db
      .select({ count: count() })
      .from(emailCampaigns);

    if (whereClause) {
      countQuery.where(whereClause);
    }

    const totalResult = await countQuery;
    const total = totalResult[0]?.count || 0;

    // Format campaigns data
    const formattedCampaigns = campaigns.map(campaign => ({
      id: campaign.id,
      name: campaign.name,
      subject: campaign.subject,
      recipientType: campaign.recipient_type,
      recipientRoles: campaign.recipient_roles ? JSON.parse(campaign.recipient_roles) : null,
      recipientUserIds: campaign.recipient_user_ids ? JSON.parse(campaign.recipient_user_ids) : null,
      status: campaign.status,
      scheduledAt: campaign.scheduled_at,
      sentAt: campaign.sent_at,
      totalRecipients: campaign.total_recipients,
      emailsSent: campaign.emails_sent,
      emailsFailed: campaign.emails_failed,
      createdBy: {
        id: campaign.created_by,
        name: campaign.creator_name || campaign.creator_username,
        username: campaign.creator_username,
      },
      createdAt: campaign.created_at,
      updatedAt: campaign.updated_at,
    }));

    const response = NextResponse.json({
      success: true,
      data: {
        campaigns: formattedCampaigns,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    });

    // Add security headers
    Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error) {
    console.error('Error fetching email campaigns:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch email campaigns' },
      { status: 500 }
    );
  }
}

// POST /api/admin/email/campaigns - Create new email campaign
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      subject,
      body: emailBody,
      recipientType,
      recipientRoles,
      recipientUserIds,
      scheduledAt,
      sendImmediately = false,
    } = body;

    // Validate required fields
    if (!name || !subject || !emailBody || !recipientType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate email content for security
    const contentValidation = validateEmailContent(emailBody);
    if (!contentValidation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email content validation failed',
          details: contentValidation.issues
        },
        { status: 400 }
      );
    }

    // Validate recipient query
    const recipientQuery = {
      type: recipientType,
      roles: recipientRoles,
      userIds: recipientUserIds,
      emailType: 'campaigns' as const,
    };

    const validation = validateRecipientQuery(recipientQuery);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Get recipients to calculate total count
    const recipients = await getEmailRecipients(recipientQuery);

    if (recipients.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid recipients found' },
        { status: 400 }
      );
    }

    const userId = parseInt(session.user.id);

    // Check rate limits
    const rateLimitCheck = await checkEmailCampaignRateLimit(userId.toString(), recipients.length);
    if (!rateLimitCheck.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: rateLimitCheck.reason,
          retryAfter: rateLimitCheck.retryAfter
        },
        { status: 429 }
      );
    }

    const now = new Date();

    // Create campaign
    const campaignResult = await db.insert(emailCampaigns).values({
      name,
      subject,
      body: emailBody,
      recipient_type: recipientType,
      recipient_roles: recipientRoles ? JSON.stringify(recipientRoles) : null,
      recipient_user_ids: recipientUserIds ? JSON.stringify(recipientUserIds) : null,
      status: sendImmediately ? 'sending' : (scheduledAt ? 'scheduled' : 'draft'),
      scheduled_at: scheduledAt ? new Date(scheduledAt) : (sendImmediately ? now : null),
      total_recipients: recipients.length,
      created_by: userId,
      created_at: now,
      updated_at: now,
    });

    const campaignId = campaignResult.insertId;

    // If sending immediately or scheduled, queue the emails
    if (sendImmediately || scheduledAt) {
      const emailsToQueue = [];
      
      for (const recipient of recipients) {
        // Process template variables
        const processedSubject = await processTemplateVariables(subject, {
          userName: recipient.displayName,
          userEmail: recipient.email,
        });
        
        const processedBody = await processTemplateVariables(emailBody, {
          userName: recipient.displayName,
          userEmail: recipient.email,
        });

        emailsToQueue.push({
          campaignId,
          recipientEmail: recipient.email,
          recipientName: recipient.displayName,
          subject: processedSubject,
          body: processedBody,
          scheduledAt: scheduledAt ? new Date(scheduledAt) : now,
        });
      }

      // Queue emails in batches
      await queueBulkEmails(emailsToQueue);

      // Update campaign status if sent immediately
      if (sendImmediately) {
        await db
          .update(emailCampaigns)
          .set({ status: 'sending' })
          .where(eq(emailCampaigns.id, campaignId));
      }
    }

    const response = NextResponse.json({
      success: true,
      data: {
        id: campaignId,
        name,
        subject,
        recipientType,
        totalRecipients: recipients.length,
        status: sendImmediately ? 'sending' : (scheduledAt ? 'scheduled' : 'draft'),
        createdAt: now,
      },
    });

    // Add security headers
    Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error) {
    console.error('Error creating email campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create email campaign' },
      { status: 500 }
    );
  }
}
