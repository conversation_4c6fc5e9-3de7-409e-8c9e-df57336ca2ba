import { NextRequest, NextResponse } from 'next/server';
import { 
  getUserPreferencesByToken, 
  unsubscribeUserByToken, 
  unsubscribeFromEmailType 
} from '@/lib/email-preferences';

// GET /api/unsubscribe?token=xxx - Show unsubscribe page data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Unsubscribe token is required' },
        { status: 400 }
      );
    }

    const result = await getUserPreferencesByToken(token);
    if (!result) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired unsubscribe token' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        user: result.user,
        preferences: result.preferences,
      },
    });

  } catch (error) {
    console.error('Error fetching unsubscribe data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch unsubscribe data' },
      { status: 500 }
    );
  }
}

// POST /api/unsubscribe - Process unsubscribe request
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, type } = body;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Unsubscribe token is required' },
        { status: 400 }
      );
    }

    let success = false;

    if (type && ['campaigns', 'notifications', 'post_updates', 'account_updates'].includes(type)) {
      // Unsubscribe from specific email type
      success = await unsubscribeFromEmailType(token, type);
    } else {
      // Unsubscribe from all emails
      success = await unsubscribeUserByToken(token);
    }

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired unsubscribe token' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        message: type 
          ? `Successfully unsubscribed from ${type.replace('_', ' ')}`
          : 'Successfully unsubscribed from all emails',
      },
    });

  } catch (error) {
    console.error('Error processing unsubscribe:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process unsubscribe request' },
      { status: 500 }
    );
  }
}
