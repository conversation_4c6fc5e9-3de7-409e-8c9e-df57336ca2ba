import {
  mysqlTable,
  varchar,
  text,
  timestamp,
  int,
  boolean,
  mysqlEnum,
  index,
  primaryKey,
  bigint,
  longtext,
  datetime,
} from 'drizzle-orm/mysql-core';
import { relations } from 'drizzle-orm';

// User status constants
export const USER_STATUS = {
  PENDING: 0,
  APPROVED: 1,
  REJECTED: 2,
} as const;

// WordPress compatible Users table
export const users = mysqlTable('wikify1h_users', {
  ID: bigint('ID', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  user_login: varchar('user_login', { length: 60 }).notNull().default(''),
  user_pass: varchar('user_pass', { length: 255 }).notNull().default(''),
  user_nicename: varchar('user_nicename', { length: 50 }).notNull().default(''),
  user_email: varchar('user_email', { length: 100 }).notNull().default(''),
  user_url: varchar('user_url', { length: 100 }).notNull().default(''),
  user_registered: datetime('user_registered', { mode: 'date' }).notNull(),
  user_activation_key: varchar('user_activation_key', { length: 255 }).notNull().default(''),
  // User status: 0=pending, 1=approved, 2=rejected
  user_status: int('user_status').notNull().default(0),
  display_name: varchar('display_name', { length: 250 }).notNull().default(''),
  user_mobile: varchar('user_mobile', { length: 20 }).notNull().default(''),
}, (table) => ({
  user_login_key: index('user_login_key').on(table.user_login),
  user_nicename_idx: index('user_nicename').on(table.user_nicename),
  user_email_idx: index('user_email').on(table.user_email),
}));

// WordPress Terms table (for categories, tags, etc.)
export const terms = mysqlTable('wikify1h_terms', {
  term_id: bigint('term_id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  name: varchar('name', { length: 200 }).notNull().default(''),
  slug: varchar('slug', { length: 200 }).notNull().default(''),
  term_group: bigint('term_group', { mode: 'number' }).notNull().default(0),
}, (table) => ({
  slug_idx: index('slug').on(table.slug),
  name_idx: index('name').on(table.name),
}));

// WordPress Term Taxonomy table
export const term_taxonomy = mysqlTable('wikify1h_term_taxonomy', {
  term_taxonomy_id: bigint('term_taxonomy_id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  term_id: bigint('term_id', { mode: 'number', unsigned: true }).notNull().default(0),
  taxonomy: varchar('taxonomy', { length: 32 }).notNull().default(''),
  description: longtext('description').notNull(),
  parent: bigint('parent', { mode: 'number', unsigned: true }).notNull().default(0),
  count: bigint('count', { mode: 'number' }).notNull().default(0),
}, (table) => ({
  term_id_taxonomy_idx: index('term_id_taxonomy').on(table.term_id, table.taxonomy),
  taxonomy_idx: index('taxonomy').on(table.taxonomy),
}));

// WordPress Term Relationships table (post-category/tag relationships)
export const term_relationships = mysqlTable('wikify1h_term_relationships', {
  object_id: bigint('object_id', { mode: 'number', unsigned: true }).notNull().default(0),
  term_taxonomy_id: bigint('term_taxonomy_id', { mode: 'number', unsigned: true }).notNull().default(0),
  term_order: int('term_order').notNull().default(0),
}, (table) => ({
  pk: primaryKey({ columns: [table.object_id, table.term_taxonomy_id] }),
  term_taxonomy_id_idx: index('term_taxonomy_id').on(table.term_taxonomy_id),
}));

// WordPress compatible Posts table
export const posts = mysqlTable('wikify1h_posts', {
  ID: bigint('ID', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  post_author: bigint('post_author', { mode: 'number', unsigned: true }).notNull().default(0),
  post_date: datetime('post_date', { mode: 'date' }).notNull(),
  post_date_gmt: datetime('post_date_gmt', { mode: 'date' }).notNull(),
  post_content: longtext('post_content').notNull(),
  post_title: text('post_title').notNull(),
  post_excerpt: text('post_excerpt').notNull(),
  post_status: varchar('post_status', { length: 20 }).notNull().default('pending'),
  comment_status: varchar('comment_status', { length: 20 }).notNull().default('open'),
  ping_status: varchar('ping_status', { length: 20 }).notNull().default('open'),
  post_password: varchar('post_password', { length: 255 }).notNull().default(''),
  post_name: varchar('post_name', { length: 200 }).notNull().default(''),
  to_ping: text('to_ping').notNull(),
  pinged: text('pinged').notNull(),
  post_modified: datetime('post_modified', { mode: 'date' }).notNull(),
  post_modified_gmt: datetime('post_modified_gmt', { mode: 'date' }).notNull(),
  post_content_filtered: longtext('post_content_filtered').notNull(),
  post_parent: bigint('post_parent', { mode: 'number', unsigned: true }).notNull().default(0),
  guid: varchar('guid', { length: 255 }).notNull().default(''),
  menu_order: int('menu_order').notNull().default(0),
  post_type: varchar('post_type', { length: 20 }).notNull().default('post'),
  post_mime_type: varchar('post_mime_type', { length: 100 }).notNull().default(''),
  comment_count: bigint('comment_count', { mode: 'number' }).notNull().default(0),
}, (table) => ({
  post_name_idx: index('post_name').on(table.post_name),
  type_status_date_idx: index('type_status_date').on(table.post_type, table.post_status, table.post_date, table.ID),
  post_parent_idx: index('post_parent').on(table.post_parent),
  post_author_idx: index('post_author').on(table.post_author),
}));



// WordPress compatible Comments table
export const comments = mysqlTable('wikify1h_comments', {
  comment_ID: bigint('comment_ID', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  comment_post_ID: bigint('comment_post_ID', { mode: 'number', unsigned: true }).notNull().default(0),
  comment_author: text('comment_author').notNull(),
  comment_author_email: varchar('comment_author_email', { length: 100 }).notNull().default(''),
  comment_author_url: varchar('comment_author_url', { length: 200 }).notNull().default(''),
  comment_author_IP: varchar('comment_author_IP', { length: 100 }).notNull().default(''),
  comment_date: datetime('comment_date', { mode: 'date' }).notNull(),
  comment_date_gmt: datetime('comment_date_gmt', { mode: 'date' }).notNull(),
  comment_content: text('comment_content').notNull(),
  comment_karma: int('comment_karma').notNull().default(0),
  comment_approved: varchar('comment_approved', { length: 20 }).notNull().default('1'),
  comment_agent: varchar('comment_agent', { length: 255 }).notNull().default(''),
  comment_type: varchar('comment_type', { length: 20 }).notNull().default('comment'),
  comment_parent: bigint('comment_parent', { mode: 'number', unsigned: true }).notNull().default(0),
  user_id: bigint('user_id', { mode: 'number', unsigned: true }).notNull().default(0),
}, (table) => ({
  comment_post_ID_idx: index('comment_post_ID').on(table.comment_post_ID),
  comment_approved_date_gmt_idx: index('comment_approved_date_gmt').on(table.comment_approved, table.comment_date_gmt),
  comment_date_gmt_idx: index('comment_date_gmt').on(table.comment_date_gmt),
  comment_parent_idx: index('comment_parent').on(table.comment_parent),
  comment_author_email_idx: index('comment_author_email').on(table.comment_author_email),
}));

// WordPress User Meta table
export const usermeta = mysqlTable('wikify1h_usermeta', {
  umeta_id: bigint('umeta_id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  user_id: bigint('user_id', { mode: 'number', unsigned: true }).notNull().default(0),
  meta_key: varchar('meta_key', { length: 255 }),
  meta_value: longtext('meta_value'),
}, (table) => ({
  user_id_idx: index('user_id').on(table.user_id),
  meta_key_idx: index('meta_key').on(table.meta_key),
}));

// WordPress Post Meta table
export const postmeta = mysqlTable('wikify1h_postmeta', {
  meta_id: bigint('meta_id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  post_id: bigint('post_id', { mode: 'number', unsigned: true }).notNull().default(0),
  meta_key: varchar('meta_key', { length: 255 }),
  meta_value: longtext('meta_value'),
}, (table) => ({
  post_id_idx: index('post_id').on(table.post_id),
  meta_key_idx: index('meta_key').on(table.meta_key),
}));

// Password reset tokens table
export const passwordResetTokens = mysqlTable('password_reset_tokens', {
  id: bigint('id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  email: varchar('email', { length: 100 }).notNull(),
  token: varchar('token', { length: 255 }).notNull(),
  expires: datetime('expires', { mode: 'date' }).notNull(),
  used: boolean('used').notNull().default(false),
  created_at: datetime('created_at', { mode: 'date' }).notNull(),
}, (table) => ({
  email_idx: index('email_idx').on(table.email),
  token_idx: index('token_idx').on(table.token),
  expires_idx: index('expires_idx').on(table.expires),
}));

// NextAuth.js required tables
export const accounts = mysqlTable('accounts', {
  userId: varchar('user_id', { length: 255 }).notNull(),
  type: varchar('type', { length: 255 }).notNull(),
  provider: varchar('provider', { length: 255 }).notNull(),
  providerAccountId: varchar('provider_account_id', { length: 255 }).notNull(),
  refresh_token: text('refresh_token'),
  access_token: text('access_token'),
  expires_at: int('expires_at'),
  token_type: varchar('token_type', { length: 255 }),
  scope: varchar('scope', { length: 255 }),
  id_token: text('id_token'),
  session_state: varchar('session_state', { length: 255 }),
}, (table) => ({
  pk: primaryKey({ columns: [table.provider, table.providerAccountId] }),
  userIdx: index('user_idx').on(table.userId),
}));

export const sessions = mysqlTable('sessions', {
  sessionToken: varchar('session_token', { length: 255 }).primaryKey(),
  userId: varchar('user_id', { length: 255 }).notNull(),
  expires: timestamp('expires').notNull(),
}, (table) => ({
  userIdx: index('user_idx').on(table.userId),
}));

export const verificationTokens = mysqlTable('verification_tokens', {
  identifier: varchar('identifier', { length: 255 }).notNull(),
  token: varchar('token', { length: 255 }).notNull(),
  expires: timestamp('expires').notNull(),
}, (table) => ({
  pk: primaryKey({ columns: [table.identifier, table.token] }),
}));

// Notifications table for user notifications
export const notifications = mysqlTable('notifications', {
  id: bigint('id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  user_id: bigint('user_id', { mode: 'number', unsigned: true }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'post_edited', 'post_deleted', 'post_approved', 'post_rejected'
  title: varchar('title', { length: 255 }).notNull(),
  message: text('message').notNull(),
  post_id: bigint('post_id', { mode: 'number', unsigned: true }),
  post_title: varchar('post_title', { length: 255 }),
  admin_id: bigint('admin_id', { mode: 'number', unsigned: true }).notNull(),
  admin_name: varchar('admin_name', { length: 100 }).notNull(),
  is_read: boolean('is_read').notNull().default(false),
  created_at: datetime('created_at', { mode: 'date' }).notNull(),
  read_at: datetime('read_at', { mode: 'date' }),
}, (table) => ({
  user_idx: index('user_idx').on(table.user_id),
  type_idx: index('type_idx').on(table.type),
  created_idx: index('created_idx').on(table.created_at),
  read_idx: index('read_idx').on(table.is_read),
}));

// Post status history table for audit trail
export const post_status_history = mysqlTable('post_status_history', {
  id: bigint('id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  post_id: bigint('post_id', { mode: 'number', unsigned: true }).notNull(),
  old_status: varchar('old_status', { length: 20 }).notNull(),
  new_status: varchar('new_status', { length: 20 }).notNull(),
  changed_by: bigint('changed_by', { mode: 'number', unsigned: true }).notNull(),
  changed_by_name: varchar('changed_by_name', { length: 100 }).notNull(),
  rejection_reason: text('rejection_reason'), // Only used when status changes to 'rejected'
  created_at: datetime('created_at', { mode: 'date' }).notNull(),
}, (table) => ({
  post_idx: index('post_idx').on(table.post_id),
  status_idx: index('status_idx').on(table.new_status),
  created_idx: index('created_idx').on(table.created_at),
  changed_by_idx: index('changed_by_idx').on(table.changed_by),
}));

// Relations for WordPress compatible schema
export const usersRelations = relations(users, ({ many }) => ({
  posts: many(posts),
  comments: many(comments),
  usermeta: many(usermeta),
  accounts: many(accounts),
  sessions: many(sessions),
}));

export const postsRelations = relations(posts, ({ one, many }) => ({
  author: one(users, {
    fields: [posts.post_author],
    references: [users.ID],
  }),
  comments: many(comments),
  postmeta: many(postmeta),
  term_relationships: many(term_relationships),
  status_history: many(post_status_history),
}));

export const commentsRelations = relations(comments, ({ one, many }) => ({
  post: one(posts, {
    fields: [comments.comment_post_ID],
    references: [posts.ID],
  }),
  author: one(users, {
    fields: [comments.user_id],
    references: [users.ID],
  }),
  parent: one(comments, {
    fields: [comments.comment_parent],
    references: [comments.comment_ID],
  }),
  replies: many(comments),
}));

export const termsRelations = relations(terms, ({ many }) => ({
  term_taxonomy: many(term_taxonomy),
}));

export const termTaxonomyRelations = relations(term_taxonomy, ({ one, many }) => ({
  term: one(terms, {
    fields: [term_taxonomy.term_id],
    references: [terms.term_id],
  }),
  term_relationships: many(term_relationships),
}));

export const termRelationshipsRelations = relations(term_relationships, ({ one }) => ({
  post: one(posts, {
    fields: [term_relationships.object_id],
    references: [posts.ID],
  }),
  term_taxonomy: one(term_taxonomy, {
    fields: [term_relationships.term_taxonomy_id],
    references: [term_taxonomy.term_taxonomy_id],
  }),
}));

export const usermetaRelations = relations(usermeta, ({ one }) => ({
  user: one(users, {
    fields: [usermeta.user_id],
    references: [users.ID],
  }),
}));

export const postmetaRelations = relations(postmeta, ({ one }) => ({
  post: one(posts, {
    fields: [postmeta.post_id],
    references: [posts.ID],
  }),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.ID],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.ID],
  }),
}));

export const postStatusHistoryRelations = relations(post_status_history, ({ one }) => ({
  post: one(posts, {
    fields: [post_status_history.post_id],
    references: [posts.ID],
  }),
  changed_by_user: one(users, {
    fields: [post_status_history.changed_by],
    references: [users.ID],
  }),
}));

// Email system relations
export const emailTemplatesRelations = relations(emailTemplates, ({ one }) => ({
  created_by_user: one(users, {
    fields: [emailTemplates.created_by],
    references: [users.ID],
  }),
}));

export const emailCampaignsRelations = relations(emailCampaigns, ({ one, many }) => ({
  created_by_user: one(users, {
    fields: [emailCampaigns.created_by],
    references: [users.ID],
  }),
  queue_items: many(emailQueue),
  delivery_logs: many(emailDeliveryLog),
}));

export const emailQueueRelations = relations(emailQueue, ({ one, many }) => ({
  campaign: one(emailCampaigns, {
    fields: [emailQueue.campaign_id],
    references: [emailCampaigns.id],
  }),
  delivery_logs: many(emailDeliveryLog),
}));

export const userEmailPreferencesRelations = relations(userEmailPreferences, ({ one }) => ({
  user: one(users, {
    fields: [userEmailPreferences.user_id],
    references: [users.ID],
  }),
}));

export const emailDeliveryLogRelations = relations(emailDeliveryLog, ({ one }) => ({
  campaign: one(emailCampaigns, {
    fields: [emailDeliveryLog.campaign_id],
    references: [emailCampaigns.id],
  }),
  queue_item: one(emailQueue, {
    fields: [emailDeliveryLog.queue_id],
    references: [emailQueue.id],
  }),
}));

// WordPress compatible Options table for site settings
export const options = mysqlTable('wikify1h_options', {
  option_id: bigint('option_id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  option_name: varchar('option_name', { length: 191 }).notNull().default(''),
  option_value: longtext('option_value').notNull(),
  autoload: varchar('autoload', { length: 20 }).notNull().default('yes'),
}, (table) => ({
  option_name_idx: index('option_name').on(table.option_name),
}));

// Email Campaign Templates table
export const emailTemplates = mysqlTable('email_templates', {
  id: bigint('id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull(),
  subject: varchar('subject', { length: 500 }).notNull(),
  body: longtext('body').notNull(),
  variables: text('variables'), // JSON string of available variables
  is_system: boolean('is_system').notNull().default(false), // System templates vs user-created
  created_by: bigint('created_by', { mode: 'number', unsigned: true }).notNull(),
  created_at: datetime('created_at', { mode: 'date' }).notNull(),
  updated_at: datetime('updated_at', { mode: 'date' }).notNull(),
}, (table) => ({
  name_idx: index('name_idx').on(table.name),
  created_by_idx: index('created_by_idx').on(table.created_by),
  is_system_idx: index('is_system_idx').on(table.is_system),
}));

// Email Campaigns table
export const emailCampaigns = mysqlTable('email_campaigns', {
  id: bigint('id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull(),
  subject: varchar('subject', { length: 500 }).notNull(),
  body: longtext('body').notNull(),
  recipient_type: mysqlEnum('recipient_type', ['all_users', 'role_based', 'individual_users', 'custom_group']).notNull(),
  recipient_roles: text('recipient_roles'), // JSON array of roles for role_based
  recipient_user_ids: text('recipient_user_ids'), // JSON array of user IDs for individual_users
  status: mysqlEnum('status', ['draft', 'scheduled', 'sending', 'sent', 'cancelled', 'failed']).notNull().default('draft'),
  scheduled_at: datetime('scheduled_at', { mode: 'date' }),
  sent_at: datetime('sent_at', { mode: 'date' }),
  total_recipients: int('total_recipients').notNull().default(0),
  emails_sent: int('emails_sent').notNull().default(0),
  emails_failed: int('emails_failed').notNull().default(0),
  created_by: bigint('created_by', { mode: 'number', unsigned: true }).notNull(),
  created_at: datetime('created_at', { mode: 'date' }).notNull(),
  updated_at: datetime('updated_at', { mode: 'date' }).notNull(),
}, (table) => ({
  status_idx: index('status_idx').on(table.status),
  recipient_type_idx: index('recipient_type_idx').on(table.recipient_type),
  created_by_idx: index('created_by_idx').on(table.created_by),
  scheduled_at_idx: index('scheduled_at_idx').on(table.scheduled_at),
  created_at_idx: index('created_at_idx').on(table.created_at),
}));

// Email Queue table for processing bulk emails
export const emailQueue = mysqlTable('email_queue', {
  id: bigint('id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  campaign_id: bigint('campaign_id', { mode: 'number', unsigned: true }),
  recipient_email: varchar('recipient_email', { length: 255 }).notNull(),
  recipient_name: varchar('recipient_name', { length: 255 }).notNull().default(''),
  subject: varchar('subject', { length: 500 }).notNull(),
  body: longtext('body').notNull(),
  status: mysqlEnum('status', ['pending', 'sending', 'sent', 'failed', 'bounced']).notNull().default('pending'),
  attempts: int('attempts').notNull().default(0),
  max_attempts: int('max_attempts').notNull().default(3),
  error_message: text('error_message'),
  scheduled_at: datetime('scheduled_at', { mode: 'date' }).notNull(),
  sent_at: datetime('sent_at', { mode: 'date' }),
  created_at: datetime('created_at', { mode: 'date' }).notNull(),
}, (table) => ({
  campaign_id_idx: index('campaign_id_idx').on(table.campaign_id),
  status_idx: index('status_idx').on(table.status),
  recipient_email_idx: index('recipient_email_idx').on(table.recipient_email),
  scheduled_at_idx: index('scheduled_at_idx').on(table.scheduled_at),
  attempts_idx: index('attempts_idx').on(table.attempts),
}));

// User Email Preferences table
export const userEmailPreferences = mysqlTable('user_email_preferences', {
  id: bigint('id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  user_id: bigint('user_id', { mode: 'number', unsigned: true }).notNull(),
  email_campaigns: boolean('email_campaigns').notNull().default(true),
  email_notifications: boolean('email_notifications').notNull().default(true),
  post_updates: boolean('post_updates').notNull().default(true),
  account_updates: boolean('account_updates').notNull().default(true),
  unsubscribe_token: varchar('unsubscribe_token', { length: 255 }),
  created_at: datetime('created_at', { mode: 'date' }).notNull(),
  updated_at: datetime('updated_at', { mode: 'date' }).notNull(),
}, (table) => ({
  user_id_idx: index('user_id_idx').on(table.user_id),
  unsubscribe_token_idx: index('unsubscribe_token_idx').on(table.unsubscribe_token),
}));

// Email Delivery Log table for tracking and analytics
export const emailDeliveryLog = mysqlTable('email_delivery_log', {
  id: bigint('id', { mode: 'number', unsigned: true }).primaryKey().autoincrement(),
  campaign_id: bigint('campaign_id', { mode: 'number', unsigned: true }),
  queue_id: bigint('queue_id', { mode: 'number', unsigned: true }),
  recipient_email: varchar('recipient_email', { length: 255 }).notNull(),
  subject: varchar('subject', { length: 500 }).notNull(),
  status: mysqlEnum('status', ['sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained', 'unsubscribed']).notNull(),
  event_data: text('event_data'), // JSON data for additional event information
  user_agent: varchar('user_agent', { length: 500 }),
  ip_address: varchar('ip_address', { length: 45 }),
  created_at: datetime('created_at', { mode: 'date' }).notNull(),
}, (table) => ({
  campaign_id_idx: index('campaign_id_idx').on(table.campaign_id),
  queue_id_idx: index('queue_id_idx').on(table.queue_id),
  recipient_email_idx: index('recipient_email_idx').on(table.recipient_email),
  status_idx: index('status_idx').on(table.status),
  created_at_idx: index('created_at_idx').on(table.created_at),
}));
