import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { emailCampaigns, emailQueue, emailDeliveryLog, users } from '@/lib/db/schema';
import { eq, and, count } from 'drizzle-orm';
import { getEmailRecipients, validateRecipientQuery } from '@/lib/email-targeting';
import { processTemplateVariables } from '@/lib/email-templates';
import { queueBulkEmails } from '@/lib/email-queue';

// GET /api/admin/email/campaigns/[id] - Get campaign details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const campaignId = parseInt(params.id);
    if (isNaN(campaignId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid campaign ID' },
        { status: 400 }
      );
    }

    // Get campaign with creator info
    const campaignResult = await db
      .select({
        id: emailCampaigns.id,
        name: emailCampaigns.name,
        subject: emailCampaigns.subject,
        body: emailCampaigns.body,
        recipient_type: emailCampaigns.recipient_type,
        recipient_roles: emailCampaigns.recipient_roles,
        recipient_user_ids: emailCampaigns.recipient_user_ids,
        status: emailCampaigns.status,
        scheduled_at: emailCampaigns.scheduled_at,
        sent_at: emailCampaigns.sent_at,
        total_recipients: emailCampaigns.total_recipients,
        emails_sent: emailCampaigns.emails_sent,
        emails_failed: emailCampaigns.emails_failed,
        created_by: emailCampaigns.created_by,
        created_at: emailCampaigns.created_at,
        updated_at: emailCampaigns.updated_at,
        creator_name: users.display_name,
        creator_username: users.user_login,
      })
      .from(emailCampaigns)
      .leftJoin(users, eq(emailCampaigns.created_by, users.ID))
      .where(eq(emailCampaigns.id, campaignId))
      .limit(1);

    if (campaignResult.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Campaign not found' },
        { status: 404 }
      );
    }

    const campaign = campaignResult[0];

    // Get delivery statistics
    const deliveryStats = await db
      .select({
        status: emailDeliveryLog.status,
        count: count(),
      })
      .from(emailDeliveryLog)
      .where(eq(emailDeliveryLog.campaign_id, campaignId))
      .groupBy(emailDeliveryLog.status);

    const stats = deliveryStats.reduce((acc, stat) => {
      acc[stat.status] = stat.count;
      return acc;
    }, {} as Record<string, number>);

    // Get queue statistics
    const queueStats = await db
      .select({
        status: emailQueue.status,
        count: count(),
      })
      .from(emailQueue)
      .where(eq(emailQueue.campaign_id, campaignId))
      .groupBy(emailQueue.status);

    const queueStatsObj = queueStats.reduce((acc, stat) => {
      acc[stat.status] = stat.count;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      data: {
        id: campaign.id,
        name: campaign.name,
        subject: campaign.subject,
        body: campaign.body,
        recipientType: campaign.recipient_type,
        recipientRoles: campaign.recipient_roles ? JSON.parse(campaign.recipient_roles) : null,
        recipientUserIds: campaign.recipient_user_ids ? JSON.parse(campaign.recipient_user_ids) : null,
        status: campaign.status,
        scheduledAt: campaign.scheduled_at,
        sentAt: campaign.sent_at,
        totalRecipients: campaign.total_recipients,
        emailsSent: campaign.emails_sent,
        emailsFailed: campaign.emails_failed,
        createdBy: {
          id: campaign.created_by,
          name: campaign.creator_name || campaign.creator_username,
          username: campaign.creator_username,
        },
        createdAt: campaign.created_at,
        updatedAt: campaign.updated_at,
        deliveryStats: stats,
        queueStats: queueStatsObj,
      },
    });

  } catch (error) {
    console.error('Error fetching campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch campaign' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/email/campaigns/[id] - Update campaign
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const campaignId = parseInt(params.id);
    if (isNaN(campaignId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid campaign ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, subject, emailBody, recipientType, recipientRoles, recipientUserIds, scheduledAt } = body;

    // Check if campaign exists and is editable
    const existingCampaign = await db
      .select()
      .from(emailCampaigns)
      .where(eq(emailCampaigns.id, campaignId))
      .limit(1);

    if (existingCampaign.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Campaign not found' },
        { status: 404 }
      );
    }

    const campaign = existingCampaign[0];

    // Only allow editing of draft and scheduled campaigns
    if (!['draft', 'scheduled'].includes(campaign.status)) {
      return NextResponse.json(
        { success: false, error: 'Cannot edit campaign that has been sent or is currently sending' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date(),
    };

    if (name !== undefined) updateData.name = name;
    if (subject !== undefined) updateData.subject = subject;
    if (emailBody !== undefined) updateData.body = emailBody;
    if (recipientType !== undefined) updateData.recipient_type = recipientType;
    if (recipientRoles !== undefined) updateData.recipient_roles = JSON.stringify(recipientRoles);
    if (recipientUserIds !== undefined) updateData.recipient_user_ids = JSON.stringify(recipientUserIds);
    if (scheduledAt !== undefined) {
      updateData.scheduled_at = scheduledAt ? new Date(scheduledAt) : null;
      updateData.status = scheduledAt ? 'scheduled' : 'draft';
    }

    // If recipient settings changed, recalculate recipient count
    if (recipientType !== undefined || recipientRoles !== undefined || recipientUserIds !== undefined) {
      const recipientQuery = {
        type: recipientType || campaign.recipient_type,
        roles: recipientRoles || (campaign.recipient_roles ? JSON.parse(campaign.recipient_roles) : null),
        userIds: recipientUserIds || (campaign.recipient_user_ids ? JSON.parse(campaign.recipient_user_ids) : null),
        emailType: 'campaigns' as const,
      };

      const validation = validateRecipientQuery(recipientQuery);
      if (!validation.isValid) {
        return NextResponse.json(
          { success: false, error: validation.errors.join(', ') },
          { status: 400 }
        );
      }

      const recipients = await getEmailRecipients(recipientQuery);
      updateData.total_recipients = recipients.length;

      // If campaign was scheduled, update the queue
      if (campaign.status === 'scheduled') {
        // Remove existing queue items
        await db
          .delete(emailQueue)
          .where(
            and(
              eq(emailQueue.campaign_id, campaignId),
              eq(emailQueue.status, 'pending')
            )
          );

        // Add new queue items if there are recipients
        if (recipients.length > 0) {
          const emailsToQueue = [];
          
          for (const recipient of recipients) {
            const processedSubject = await processTemplateVariables(
              subject || campaign.subject, 
              {
                userName: recipient.displayName,
                userEmail: recipient.email,
              }
            );
            
            const processedBody = await processTemplateVariables(
              emailBody || campaign.body, 
              {
                userName: recipient.displayName,
                userEmail: recipient.email,
              }
            );

            emailsToQueue.push({
              campaignId,
              recipientEmail: recipient.email,
              recipientName: recipient.displayName,
              subject: processedSubject,
              body: processedBody,
              scheduledAt: scheduledAt ? new Date(scheduledAt) : campaign.scheduled_at || new Date(),
            });
          }

          await queueBulkEmails(emailsToQueue);
        }
      }
    }

    // Update campaign
    await db
      .update(emailCampaigns)
      .set(updateData)
      .where(eq(emailCampaigns.id, campaignId));

    return NextResponse.json({
      success: true,
      data: {
        id: campaignId,
        message: 'Campaign updated successfully',
      },
    });

  } catch (error) {
    console.error('Error updating campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update campaign' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/email/campaigns/[id] - Delete campaign
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const campaignId = parseInt(params.id);
    if (isNaN(campaignId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid campaign ID' },
        { status: 400 }
      );
    }

    // Check if campaign exists
    const existingCampaign = await db
      .select()
      .from(emailCampaigns)
      .where(eq(emailCampaigns.id, campaignId))
      .limit(1);

    if (existingCampaign.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Campaign not found' },
        { status: 404 }
      );
    }

    const campaign = existingCampaign[0];

    // Only allow deletion of draft campaigns
    if (campaign.status !== 'draft') {
      return NextResponse.json(
        { success: false, error: 'Can only delete draft campaigns' },
        { status: 400 }
      );
    }

    // Delete campaign (cascade will handle related records)
    await db
      .delete(emailCampaigns)
      .where(eq(emailCampaigns.id, campaignId));

    return NextResponse.json({
      success: true,
      data: {
        message: 'Campaign deleted successfully',
      },
    });

  } catch (error) {
    console.error('Error deleting campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete campaign' },
      { status: 500 }
    );
  }
}
