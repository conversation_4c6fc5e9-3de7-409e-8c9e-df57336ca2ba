import { db } from '@/lib/db';
import { sql } from 'drizzle-orm';

async function createEmailSystemTables() {
  try {
    console.log('Creating email system tables...');
    
    // Create email_templates table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS email_templates (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name VARCHA<PERSON>(255) NOT NULL,
        subject VARCHAR(500) NOT NULL,
        body LONGTEXT NOT NULL,
        variables TEXT,
        is_system BOOLEAN NOT NULL DEFAULT FALSE,
        created_by BIGINT UNSIGNED NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        INDEX name_idx (name),
        INDEX created_by_idx (created_by),
        INDEX is_system_idx (is_system),
        FOREIGN KEY (created_by) REFERENCES wikify1h_users(ID) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ email_templates table created');

    // Create email_campaigns table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS email_campaigns (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        subject VARCHAR(500) NOT NULL,
        body LONGTEXT NOT NULL,
        recipient_type ENUM('all_users', 'role_based', 'individual_users', 'custom_group') NOT NULL,
        recipient_roles TEXT,
        recipient_user_ids TEXT,
        status ENUM('draft', 'scheduled', 'sending', 'sent', 'cancelled', 'failed') NOT NULL DEFAULT 'draft',
        scheduled_at DATETIME NULL,
        sent_at DATETIME NULL,
        total_recipients INT NOT NULL DEFAULT 0,
        emails_sent INT NOT NULL DEFAULT 0,
        emails_failed INT NOT NULL DEFAULT 0,
        created_by BIGINT UNSIGNED NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        INDEX status_idx (status),
        INDEX recipient_type_idx (recipient_type),
        INDEX created_by_idx (created_by),
        INDEX scheduled_at_idx (scheduled_at),
        INDEX created_at_idx (created_at),
        FOREIGN KEY (created_by) REFERENCES wikify1h_users(ID) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ email_campaigns table created');

    // Create email_queue table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS email_queue (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        campaign_id BIGINT UNSIGNED NULL,
        recipient_email VARCHAR(255) NOT NULL,
        recipient_name VARCHAR(255) NOT NULL DEFAULT '',
        subject VARCHAR(500) NOT NULL,
        body LONGTEXT NOT NULL,
        status ENUM('pending', 'sending', 'sent', 'failed', 'bounced') NOT NULL DEFAULT 'pending',
        attempts INT NOT NULL DEFAULT 0,
        max_attempts INT NOT NULL DEFAULT 3,
        error_message TEXT,
        scheduled_at DATETIME NOT NULL,
        sent_at DATETIME NULL,
        created_at DATETIME NOT NULL,
        INDEX campaign_id_idx (campaign_id),
        INDEX status_idx (status),
        INDEX recipient_email_idx (recipient_email),
        INDEX scheduled_at_idx (scheduled_at),
        INDEX attempts_idx (attempts),
        FOREIGN KEY (campaign_id) REFERENCES email_campaigns(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ email_queue table created');

    // Create user_email_preferences table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS user_email_preferences (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id BIGINT UNSIGNED NOT NULL,
        email_campaigns BOOLEAN NOT NULL DEFAULT TRUE,
        email_notifications BOOLEAN NOT NULL DEFAULT TRUE,
        post_updates BOOLEAN NOT NULL DEFAULT TRUE,
        account_updates BOOLEAN NOT NULL DEFAULT TRUE,
        unsubscribe_token VARCHAR(255),
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        UNIQUE KEY user_id_unique (user_id),
        INDEX user_id_idx (user_id),
        INDEX unsubscribe_token_idx (unsubscribe_token),
        FOREIGN KEY (user_id) REFERENCES wikify1h_users(ID) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ user_email_preferences table created');

    // Create email_delivery_log table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS email_delivery_log (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        campaign_id BIGINT UNSIGNED NULL,
        queue_id BIGINT UNSIGNED NULL,
        recipient_email VARCHAR(255) NOT NULL,
        subject VARCHAR(500) NOT NULL,
        status ENUM('sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained', 'unsubscribed') NOT NULL,
        event_data TEXT,
        user_agent VARCHAR(500),
        ip_address VARCHAR(45),
        created_at DATETIME NOT NULL,
        INDEX campaign_id_idx (campaign_id),
        INDEX queue_id_idx (queue_id),
        INDEX recipient_email_idx (recipient_email),
        INDEX status_idx (status),
        INDEX created_at_idx (created_at),
        FOREIGN KEY (campaign_id) REFERENCES email_campaigns(id) ON DELETE SET NULL,
        FOREIGN KEY (queue_id) REFERENCES email_queue(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ email_delivery_log table created');

    // Insert default email templates
    await db.execute(sql`
      INSERT IGNORE INTO email_templates (name, subject, body, variables, is_system, created_by, created_at, updated_at)
      VALUES 
      ('Welcome Campaign', 'Welcome to {{siteName}}!', 
       '<h1>Welcome {{userName}}!</h1><p>Thank you for joining {{siteName}}. We are excited to have you as part of our community.</p><p>Best regards,<br>The {{siteName}} Team</p>',
       '["siteName", "userName", "userEmail"]', TRUE, 1, NOW(), NOW()),
      ('Newsletter Template', '{{siteName}} Newsletter - {{date}}',
       '<h1>{{siteName}} Newsletter</h1><p>Dear {{userName}},</p><p>Here are the latest updates from {{siteName}}:</p><div>{{content}}</div><p>Best regards,<br>The {{siteName}} Team</p>',
       '["siteName", "userName", "userEmail", "date", "content"]', TRUE, 1, NOW(), NOW()),
      ('Announcement Template', 'Important Announcement from {{siteName}}',
       '<h1>Important Announcement</h1><p>Dear {{userName}},</p><div>{{content}}</div><p>Thank you for your attention.</p><p>Best regards,<br>The {{siteName}} Team</p>',
       '["siteName", "userName", "userEmail", "content"]', TRUE, 1, NOW(), NOW())
    `);
    console.log('✅ Default email templates inserted');

    console.log('🎉 Email system tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating email system tables:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  createEmailSystemTables()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export { createEmailSystemTables };
