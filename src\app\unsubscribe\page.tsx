'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Mail, CheckCircle, AlertCircle, UserX } from 'lucide-react';

interface UserData {
  id: number;
  email: string;
  displayName: string;
}

interface EmailPreferences {
  emailCampaigns: boolean;
  emailNotifications: boolean;
  postUpdates: boolean;
  accountUpdates: boolean;
}

export default function UnsubscribePage() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [userData, setUserData] = useState<UserData | null>(null);
  const [preferences, setPreferences] = useState<EmailPreferences | null>(null);

  useEffect(() => {
    if (token) {
      fetchUnsubscribeData();
    } else {
      setError('Invalid unsubscribe link');
      setLoading(false);
    }
  }, [token]);

  const fetchUnsubscribeData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/unsubscribe?token=${token}`);
      const data = await response.json();

      if (data.success) {
        setUserData(data.data.user);
        setPreferences(data.data.preferences);
      } else {
        setError(data.error || 'Invalid unsubscribe link');
      }
    } catch (error) {
      console.error('Error fetching unsubscribe data:', error);
      setError('Failed to load unsubscribe data');
    } finally {
      setLoading(false);
    }
  };

  const handleUnsubscribeAll = async () => {
    try {
      setSubmitting(true);
      setError('');
      
      const response = await fetch('/api/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();
      if (data.success) {
        setSuccess('You have been successfully unsubscribed from all emails.');
        setPreferences({
          emailCampaigns: false,
          emailNotifications: false,
          postUpdates: false,
          accountUpdates: false,
        });
      } else {
        setError(data.error || 'Failed to unsubscribe');
      }
    } catch (error) {
      console.error('Error unsubscribing:', error);
      setError('An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUnsubscribeType = async (type: string) => {
    try {
      setSubmitting(true);
      setError('');
      
      const response = await fetch('/api/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, type }),
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(data.data.message);
        // Update preferences state
        if (preferences) {
          setPreferences({
            ...preferences,
            [type === 'campaigns' ? 'emailCampaigns' : 
             type === 'notifications' ? 'emailNotifications' :
             type === 'post_updates' ? 'postUpdates' : 'accountUpdates']: false
          });
        }
      } else {
        setError(data.error || 'Failed to unsubscribe');
      }
    } catch (error) {
      console.error('Error unsubscribing:', error);
      setError('An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <Mail className="mx-auto h-12 w-12 text-gray-400" />
          <h1 className="mt-4 text-3xl font-bold text-gray-900">Email Preferences</h1>
          <p className="mt-2 text-gray-600">
            Manage your email subscription preferences
          </p>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-6 border-green-200 bg-green-50 text-green-800">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {userData && preferences && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserX className="w-5 h-5" />
                Unsubscribe Options
              </CardTitle>
              <p className="text-sm text-gray-600">
                Email: {userData.email}
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Status */}
              <div className="space-y-3">
                <h3 className="font-medium text-gray-900">Current Email Preferences</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      checked={preferences.emailCampaigns} 
                      disabled 
                      id="campaigns"
                    />
                    <Label htmlFor="campaigns" className="text-sm">
                      Email Campaigns & Newsletters
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      checked={preferences.emailNotifications} 
                      disabled 
                      id="notifications"
                    />
                    <Label htmlFor="notifications" className="text-sm">
                      Email Notifications
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      checked={preferences.postUpdates} 
                      disabled 
                      id="post_updates"
                    />
                    <Label htmlFor="post_updates" className="text-sm">
                      Post Updates
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      checked={preferences.accountUpdates} 
                      disabled 
                      id="account_updates"
                    />
                    <Label htmlFor="account_updates" className="text-sm">
                      Account Updates
                    </Label>
                  </div>
                </div>
              </div>

              {/* Unsubscribe Options */}
              <div className="space-y-3">
                <h3 className="font-medium text-gray-900">Unsubscribe Options</h3>
                
                {/* Individual unsubscribe buttons */}
                <div className="space-y-2">
                  {preferences.emailCampaigns && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => handleUnsubscribeType('campaigns')}
                      disabled={submitting}
                    >
                      Unsubscribe from Email Campaigns
                    </Button>
                  )}
                  
                  {preferences.emailNotifications && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => handleUnsubscribeType('notifications')}
                      disabled={submitting}
                    >
                      Unsubscribe from Email Notifications
                    </Button>
                  )}
                  
                  {preferences.postUpdates && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => handleUnsubscribeType('post_updates')}
                      disabled={submitting}
                    >
                      Unsubscribe from Post Updates
                    </Button>
                  )}
                  
                  {preferences.accountUpdates && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => handleUnsubscribeType('account_updates')}
                      disabled={submitting}
                    >
                      Unsubscribe from Account Updates
                    </Button>
                  )}
                </div>

                {/* Unsubscribe from all */}
                <div className="pt-4 border-t">
                  <Button
                    variant="destructive"
                    className="w-full"
                    onClick={handleUnsubscribeAll}
                    disabled={submitting}
                  >
                    {submitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processing...
                      </>
                    ) : (
                      'Unsubscribe from All Emails'
                    )}
                  </Button>
                  <p className="text-xs text-gray-500 mt-2 text-center">
                    This will unsubscribe you from all email communications
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            If you have any questions, please contact our support team.
          </p>
        </div>
      </div>
    </div>
  );
}
