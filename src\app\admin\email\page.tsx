'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/admin-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Mail,
  Plus,
  Search,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  Users,
  Eye,
  Edit,
  Trash2,
  Calendar,
  BarChart3
} from 'lucide-react';

interface EmailCampaign {
  id: number;
  name: string;
  subject: string;
  recipientType: string;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'cancelled' | 'failed';
  scheduledAt?: string;
  sentAt?: string;
  totalRecipients: number;
  emailsSent: number;
  emailsFailed: number;
  createdBy: {
    id: number;
    name: string;
    username: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface EmailStats {
  totalCampaigns: number;
  activeCampaigns: number;
  totalEmailsSent: number;
  totalRecipients: number;
}

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  scheduled: 'bg-blue-100 text-blue-800',
  sending: 'bg-yellow-100 text-yellow-800',
  sent: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
  failed: 'bg-red-100 text-red-800',
};

const statusIcons = {
  draft: Edit,
  scheduled: Clock,
  sending: Send,
  sent: CheckCircle,
  cancelled: XCircle,
  failed: XCircle,
};

export default function EmailCampaigns() {
  const [campaigns, setCampaigns] = useState<EmailCampaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState<EmailStats>({
    totalCampaigns: 0,
    activeCampaigns: 0,
    totalEmailsSent: 0,
    totalRecipients: 0,
  });

  useEffect(() => {
    fetchCampaigns();
    fetchStats();
  }, [currentPage, searchTerm, statusFilter]);

  const fetchCampaigns = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
      });

      const response = await fetch(`/api/admin/email/campaigns?${params}`);
      const data = await response.json();

      if (data.success) {
        setCampaigns(data.data.campaigns);
        setTotalPages(data.data.pagination.totalPages);
      } else {
        console.error('Failed to fetch campaigns:', data.error);
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // This would be a separate API endpoint for email statistics
      // For now, calculate from campaigns data
      const totalCampaigns = campaigns.length;
      const activeCampaigns = campaigns.filter(c => ['scheduled', 'sending'].includes(c.status)).length;
      const totalEmailsSent = campaigns.reduce((sum, c) => sum + c.emailsSent, 0);
      const totalRecipients = campaigns.reduce((sum, c) => sum + c.totalRecipients, 0);

      setStats({
        totalCampaigns,
        activeCampaigns,
        totalEmailsSent,
        totalRecipients,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getRecipientTypeLabel = (type: string) => {
    switch (type) {
      case 'all_users':
        return 'All Users';
      case 'role_based':
        return 'Role-based';
      case 'individual_users':
        return 'Individual Users';
      case 'custom_group':
        return 'Custom Group';
      default:
        return type;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Email Campaigns</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage and send email campaigns to your users
            </p>
          </div>
          <Button asChild>
            <a href="/admin/email/campaigns/create">
              <Plus className="w-4 h-4 mr-2" />
              Create Campaign
            </a>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Campaigns</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalCampaigns}</p>
                </div>
                <Mail className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Campaigns</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.activeCampaigns}</p>
                </div>
                <Send className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Emails Sent</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalEmailsSent.toLocaleString()}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Recipients</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalRecipients.toLocaleString()}</p>
                </div>
                <Users className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search campaigns..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={statusFilter === '' ? 'default' : 'outline'}
                  onClick={() => handleStatusFilter('')}
                  size="sm"
                >
                  All
                </Button>
                <Button
                  variant={statusFilter === 'draft' ? 'default' : 'outline'}
                  onClick={() => handleStatusFilter('draft')}
                  size="sm"
                >
                  Draft
                </Button>
                <Button
                  variant={statusFilter === 'scheduled' ? 'default' : 'outline'}
                  onClick={() => handleStatusFilter('scheduled')}
                  size="sm"
                >
                  Scheduled
                </Button>
                <Button
                  variant={statusFilter === 'sent' ? 'default' : 'outline'}
                  onClick={() => handleStatusFilter('sent')}
                  size="sm"
                >
                  Sent
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Campaigns List */}
        <Card>
          <CardHeader>
            <CardTitle>Email Campaigns</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : campaigns.length === 0 ? (
              <div className="text-center py-8">
                <Mail className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No campaigns found</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {searchTerm || statusFilter ? 'Try adjusting your filters' : 'Get started by creating your first email campaign'}
                </p>
                <Button asChild>
                  <a href="/admin/email/campaigns/create">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Campaign
                  </a>
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {campaigns.map((campaign) => {
                  const StatusIcon = statusIcons[campaign.status];
                  return (
                    <div
                      key={campaign.id}
                      className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              {campaign.name}
                            </h3>
                            <Badge className={statusColors[campaign.status]}>
                              <StatusIcon className="w-3 h-3 mr-1" />
                              {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            {campaign.subject}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                            <span className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              {campaign.totalRecipients} recipients ({getRecipientTypeLabel(campaign.recipientType)})
                            </span>
                            {campaign.emailsSent > 0 && (
                              <span className="flex items-center gap-1">
                                <Send className="w-3 h-3" />
                                {campaign.emailsSent} sent
                              </span>
                            )}
                            {campaign.emailsFailed > 0 && (
                              <span className="flex items-center gap-1 text-red-500">
                                <XCircle className="w-3 h-3" />
                                {campaign.emailsFailed} failed
                              </span>
                            )}
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              Created {formatDate(campaign.createdAt)}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" asChild>
                            <a href={`/admin/email/campaigns/${campaign.id}`}>
                              <Eye className="w-4 h-4" />
                            </a>
                          </Button>
                          {campaign.status === 'draft' && (
                            <Button variant="outline" size="sm" asChild>
                              <a href={`/admin/email/campaigns/${campaign.id}/edit`}>
                                <Edit className="w-4 h-4" />
                              </a>
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-6">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
