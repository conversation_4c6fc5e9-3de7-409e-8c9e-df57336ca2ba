import { NextRequest, NextResponse } from 'next/server';
import { processEmailQueue, cleanupOldQueueEntries } from '@/lib/email-queue';

// POST /api/cron/process-email-queue - Process pending emails in the queue
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a cron job or authorized source
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'your-secret-key';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('🚀 Starting email queue processing job...');
    
    // Process the email queue
    await processEmailQueue();
    
    // Clean up old queue entries (run less frequently)
    const shouldCleanup = Math.random() < 0.1; // 10% chance
    if (shouldCleanup) {
      console.log('🧹 Running cleanup job...');
      await cleanupOldQueueEntries();
    }

    return NextResponse.json({
      success: true,
      data: {
        message: 'Email queue processed successfully',
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('❌ Error processing email queue:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process email queue' },
      { status: 500 }
    );
  }
}

// GET /api/cron/process-email-queue - Health check for the cron job
export async function GET(request: NextRequest) {
  return NextResponse.json({
    success: true,
    data: {
      message: 'Email queue processor is healthy',
      timestamp: new Date().toISOString(),
    },
  });
}
