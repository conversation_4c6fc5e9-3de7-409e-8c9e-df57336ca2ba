'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/admin/admin-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Mail,
  Users,
  Eye,
  Send,
  Save,
  ArrowLeft,
  AlertCircle,
  Calendar,
  Clock,
  UserCheck,
  UserX
} from 'lucide-react';

interface EmailRecipient {
  id: number;
  email: string;
  displayName: string;
  username: string;
  role: string;
  canReceiveCampaigns: boolean;
}

interface RecipientPreview {
  recipients: EmailRecipient[];
  stats: {
    total: number;
    canReceiveCampaigns: number;
    optedOut: number;
    byRole: Record<string, number>;
  };
}

export default function CreateEmailCampaign() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form data
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    body: '',
    recipientType: 'all_users',
    recipientRoles: [] as string[],
    recipientUserIds: [] as number[],
    scheduledAt: '',
    sendImmediately: false,
  });

  // Recipients preview
  const [recipientPreview, setRecipientPreview] = useState<RecipientPreview | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Available roles
  const availableRoles = [
    { value: 'ADMIN', label: 'Administrators' },
    { value: 'EDITOR', label: 'Editors' },
    { value: 'AUTHOR', label: 'Authors' },
  ];

  useEffect(() => {
    if (formData.recipientType && (
      formData.recipientType === 'all_users' ||
      (formData.recipientType === 'role_based' && formData.recipientRoles.length > 0) ||
      (formData.recipientType === 'individual_users' && formData.recipientUserIds.length > 0)
    )) {
      previewRecipients();
    }
  }, [formData.recipientType, formData.recipientRoles, formData.recipientUserIds]);

  const previewRecipients = async () => {
    try {
      setPreviewLoading(true);
      const response = await fetch('/api/admin/email/recipients/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipientType: formData.recipientType,
          recipientRoles: formData.recipientRoles,
          recipientUserIds: formData.recipientUserIds,
        }),
      });

      const data = await response.json();
      if (data.success) {
        setRecipientPreview(data.data);
      } else {
        console.error('Failed to preview recipients:', data.error);
      }
    } catch (error) {
      console.error('Error previewing recipients:', error);
    } finally {
      setPreviewLoading(false);
    }
  };

  const handleRoleChange = (role: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      recipientRoles: checked
        ? [...prev.recipientRoles, role]
        : prev.recipientRoles.filter(r => r !== role)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validation
    if (!formData.name.trim()) {
      setError('Campaign name is required');
      return;
    }
    if (!formData.subject.trim()) {
      setError('Email subject is required');
      return;
    }
    if (!formData.body.trim()) {
      setError('Email body is required');
      return;
    }

    if (formData.recipientType === 'role_based' && formData.recipientRoles.length === 0) {
      setError('Please select at least one role');
      return;
    }

    if (formData.recipientType === 'individual_users' && formData.recipientUserIds.length === 0) {
      setError('Please select at least one user');
      return;
    }

    if (!formData.sendImmediately && formData.scheduledAt) {
      const scheduledDate = new Date(formData.scheduledAt);
      if (scheduledDate <= new Date()) {
        setError('Scheduled date must be in the future');
        return;
      }
    }

    try {
      setLoading(true);
      const response = await fetch('/api/admin/email/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          subject: formData.subject,
          body: formData.body,
          recipientType: formData.recipientType,
          recipientRoles: formData.recipientRoles.length > 0 ? formData.recipientRoles : null,
          recipientUserIds: formData.recipientUserIds.length > 0 ? formData.recipientUserIds : null,
          scheduledAt: formData.scheduledAt || null,
          sendImmediately: formData.sendImmediately,
        }),
      });

      const data = await response.json();
      if (data.success) {
        setSuccess('Email campaign created successfully!');
        setTimeout(() => {
          router.push('/admin/email');
        }, 2000);
      } else {
        setError(data.error || 'Failed to create email campaign');
      }
    } catch (error) {
      console.error('Error creating campaign:', error);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDraft = async () => {
    setError('');
    setSuccess('');

    if (!formData.name.trim()) {
      setError('Campaign name is required to save draft');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/admin/email/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          subject: formData.subject || 'Draft Email',
          body: formData.body || '<p>Draft content</p>',
          recipientType: formData.recipientType,
          recipientRoles: formData.recipientRoles.length > 0 ? formData.recipientRoles : null,
          recipientUserIds: formData.recipientUserIds.length > 0 ? formData.recipientUserIds : null,
          sendImmediately: false,
        }),
      });

      const data = await response.json();
      if (data.success) {
        setSuccess('Draft saved successfully!');
        setTimeout(() => {
          router.push('/admin/email');
        }, 2000);
      } else {
        setError(data.error || 'Failed to save draft');
      }
    } catch (error) {
      console.error('Error saving draft:', error);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Create Email Campaign</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Compose and send emails to your users
            </p>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50 text-green-800">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Campaign Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="w-5 h-5" />
                    Campaign Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name">Campaign Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter campaign name"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="subject">Email Subject *</Label>
                    <Input
                      id="subject"
                      value={formData.subject}
                      onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                      placeholder="Enter email subject"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="body">Email Body *</Label>
                    <Textarea
                      id="body"
                      value={formData.body}
                      onChange={(e) => setFormData(prev => ({ ...prev, body: e.target.value }))}
                      placeholder="Enter email content (HTML supported)"
                      rows={10}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      You can use variables like {{`{userName}`}} and {{`{siteName}`}} in your content
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Recipients */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Recipients
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Recipient Type</Label>
                    <Select
                      value={formData.recipientType}
                      onValueChange={(value) => setFormData(prev => ({ 
                        ...prev, 
                        recipientType: value,
                        recipientRoles: [],
                        recipientUserIds: []
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all_users">All Users</SelectItem>
                        <SelectItem value="role_based">Role-based</SelectItem>
                        <SelectItem value="individual_users">Individual Users</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {formData.recipientType === 'role_based' && (
                    <div>
                      <Label>Select Roles</Label>
                      <div className="space-y-2 mt-2">
                        {availableRoles.map((role) => (
                          <div key={role.value} className="flex items-center space-x-2">
                            <Checkbox
                              id={role.value}
                              checked={formData.recipientRoles.includes(role.value)}
                              onCheckedChange={(checked) => handleRoleChange(role.value, checked as boolean)}
                            />
                            <Label htmlFor={role.value}>{role.label}</Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {formData.recipientType === 'individual_users' && (
                    <div>
                      <Label>Select Users</Label>
                      <p className="text-sm text-gray-500 mt-1">
                        Individual user selection will be implemented in the next phase
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Scheduling */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Scheduling
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="sendImmediately"
                      checked={formData.sendImmediately}
                      onCheckedChange={(checked) => setFormData(prev => ({ 
                        ...prev, 
                        sendImmediately: checked as boolean,
                        scheduledAt: checked ? '' : prev.scheduledAt
                      }))}
                    />
                    <Label htmlFor="sendImmediately">Send immediately</Label>
                  </div>

                  {!formData.sendImmediately && (
                    <div>
                      <Label htmlFor="scheduledAt">Schedule for later</Label>
                      <Input
                        id="scheduledAt"
                        type="datetime-local"
                        value={formData.scheduledAt}
                        onChange={(e) => setFormData(prev => ({ ...prev, scheduledAt: e.target.value }))}
                        min={new Date().toISOString().slice(0, 16)}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        {formData.sendImmediately ? 'Sending...' : 'Creating...'}
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        {formData.sendImmediately ? 'Send Now' : 'Create Campaign'}
                      </>
                    )}
                  </Button>

                  <Button type="button" variant="outline" className="w-full" onClick={handleSaveDraft} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    Save as Draft
                  </Button>

                  <Button 
                    type="button" 
                    variant="outline" 
                    className="w-full"
                    onClick={() => setShowPreview(!showPreview)}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    {showPreview ? 'Hide Preview' : 'Preview Email'}
                  </Button>
                </CardContent>
              </Card>

              {/* Recipients Preview */}
              {recipientPreview && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      Recipients Preview
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {previewLoading ? (
                      <div className="flex items-center justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Total Recipients</span>
                          <Badge variant="secondary">{recipientPreview.stats.total}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-green-600 flex items-center gap-1">
                            <UserCheck className="w-3 h-3" />
                            Can receive
                          </span>
                          <Badge variant="secondary">{recipientPreview.stats.canReceiveCampaigns}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-red-600 flex items-center gap-1">
                            <UserX className="w-3 h-3" />
                            Opted out
                          </span>
                          <Badge variant="secondary">{recipientPreview.stats.optedOut}</Badge>
                        </div>
                        
                        {Object.keys(recipientPreview.stats.byRole).length > 0 && (
                          <div className="pt-2 border-t">
                            <p className="text-xs font-medium text-gray-600 mb-2">By Role:</p>
                            {Object.entries(recipientPreview.stats.byRole).map(([role, count]) => (
                              <div key={role} className="flex items-center justify-between text-xs">
                                <span>{role}</span>
                                <span>{count}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </form>

        {/* Email Preview Modal */}
        {showPreview && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Email Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-white">
                <div className="border-b pb-2 mb-4">
                  <p className="text-sm text-gray-600">Subject:</p>
                  <p className="font-medium">{formData.subject || 'No subject'}</p>
                </div>
                <div 
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ 
                    __html: formData.body.replace(/\n/g, '<br>') || '<p>No content</p>' 
                  }}
                />
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
}
