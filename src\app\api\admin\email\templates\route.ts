import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { 
  getEmailTemplates, 
  createEmailTemplate, 
  validateTemplateContent,
  DEFAULT_TEMPLATE_VARIABLES 
} from '@/lib/email-templates';

// GET /api/admin/email/templates - List email templates
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const includeSystem = searchParams.get('includeSystem') === 'true';

    const result = await getEmailTemplates({
      page,
      limit,
      search,
      includeSystem,
    });

    return NextResponse.json({
      success: true,
      data: {
        templates: result.templates,
        pagination: {
          page: result.page,
          limit,
          total: result.total,
          totalPages: result.totalPages,
        },
        availableVariables: DEFAULT_TEMPLATE_VARIABLES,
      },
    });

  } catch (error) {
    console.error('Error fetching email templates:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch email templates' },
      { status: 500 }
    );
  }
}

// POST /api/admin/email/templates - Create new email template
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, subject, body: templateBody, variables } = body;

    // Validate required fields
    if (!name || !subject || !templateBody) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: name, subject, and body are required' },
        { status: 400 }
      );
    }

    // Validate template content
    const validation = validateTemplateContent(templateBody);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid template content',
          details: {
            invalidVariables: validation.invalidVariables,
            missingVariables: validation.missingVariables,
          }
        },
        { status: 400 }
      );
    }

    const userId = parseInt(session.user.id);

    const templateId = await createEmailTemplate({
      name,
      subject,
      body: templateBody,
      variables,
      createdBy: userId,
    });

    return NextResponse.json({
      success: true,
      data: {
        id: templateId,
        name,
        subject,
        message: 'Email template created successfully',
      },
    });

  } catch (error) {
    console.error('Error creating email template:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create email template' },
      { status: 500 }
    );
  }
}
