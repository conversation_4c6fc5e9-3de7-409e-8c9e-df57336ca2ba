import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { 
  getEmailRecipients, 
  getRecipientStats, 
  searchUsers,
  validateRecipientQuery 
} from '@/lib/email-targeting';

// GET /api/admin/email/recipients - Get email recipients based on criteria
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const roles = searchParams.get('roles')?.split(',').filter(Boolean);
    const userIds = searchParams.get('userIds')?.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
    const includeInactive = searchParams.get('includeInactive') === 'true';
    const emailType = searchParams.get('emailType') as 'campaigns' | 'notifications' | 'post_updates' | 'account_updates' | undefined;
    const search = searchParams.get('search');

    // Handle search requests
    if (search) {
      const searchResults = await searchUsers(search, 20);
      return NextResponse.json({
        success: true,
        data: {
          recipients: searchResults,
          total: searchResults.length,
        },
      });
    }

    // Handle stats requests
    if (type === 'stats') {
      const stats = await getRecipientStats();
      return NextResponse.json({
        success: true,
        data: stats,
      });
    }

    // Validate recipient query
    if (!type) {
      return NextResponse.json(
        { success: false, error: 'Recipient type is required' },
        { status: 400 }
      );
    }

    const recipientQuery = {
      type: type as any,
      roles,
      userIds,
      includeInactive,
      emailType,
    };

    const validation = validateRecipientQuery(recipientQuery);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Get recipients
    const recipients = await getEmailRecipients(recipientQuery);

    return NextResponse.json({
      success: true,
      data: {
        recipients,
        total: recipients.length,
        query: recipientQuery,
      },
    });

  } catch (error) {
    console.error('Error fetching email recipients:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch email recipients' },
      { status: 500 }
    );
  }
}

// POST /api/admin/email/recipients/preview - Preview recipients for a campaign
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { recipientType, recipientRoles, recipientUserIds, includeInactive = false } = body;

    // Validate recipient query
    const recipientQuery = {
      type: recipientType,
      roles: recipientRoles,
      userIds: recipientUserIds,
      includeInactive,
      emailType: 'campaigns' as const,
    };

    const validation = validateRecipientQuery(recipientQuery);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Get recipients
    const recipients = await getEmailRecipients(recipientQuery);

    // Group recipients by role for better overview
    const recipientsByRole = recipients.reduce((acc, recipient) => {
      const role = recipient.role;
      if (!acc[role]) {
        acc[role] = [];
      }
      acc[role].push(recipient);
      return acc;
    }, {} as Record<string, typeof recipients>);

    // Calculate statistics
    const stats = {
      total: recipients.length,
      canReceiveCampaigns: recipients.filter(r => r.canReceiveCampaigns).length,
      optedOut: recipients.filter(r => !r.canReceiveCampaigns).length,
      byRole: Object.keys(recipientsByRole).reduce((acc, role) => {
        acc[role] = recipientsByRole[role].length;
        return acc;
      }, {} as Record<string, number>),
    };

    return NextResponse.json({
      success: true,
      data: {
        recipients: recipients.slice(0, 50), // Limit preview to first 50 recipients
        recipientsByRole,
        stats,
        query: recipientQuery,
      },
    });

  } catch (error) {
    console.error('Error previewing email recipients:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to preview email recipients' },
      { status: 500 }
    );
  }
}
