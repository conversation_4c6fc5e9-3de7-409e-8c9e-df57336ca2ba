import { db } from '@/lib/db';
import { emailTemplates, users } from '@/lib/db/schema';
import { eq, desc, like, or } from 'drizzle-orm';
import { getSiteSettings } from '@/lib/settings';

export interface EmailTemplate {
  id: number;
  name: string;
  subject: string;
  body: string;
  variables: string[];
  isSystem: boolean;
  createdBy: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTemplateParams {
  name: string;
  subject: string;
  body: string;
  variables?: string[];
  createdBy: number;
}

export interface UpdateTemplateParams {
  id: number;
  name?: string;
  subject?: string;
  body?: string;
  variables?: string[];
}

export interface TemplateVariable {
  name: string;
  description: string;
  example: string;
}

// Default template variables available in all templates
export const DEFAULT_TEMPLATE_VARIABLES: TemplateVariable[] = [
  {
    name: 'siteName',
    description: 'The name of the website',
    example: 'Wikify Blog'
  },
  {
    name: 'siteUrl',
    description: 'The URL of the website',
    example: 'https://example.com'
  },
  {
    name: 'userName',
    description: 'The recipient\'s display name',
    example: '<PERSON>'
  },
  {
    name: 'userEmail',
    description: 'The recipient\'s email address',
    example: '<EMAIL>'
  },
  {
    name: 'currentDate',
    description: 'Current date in readable format',
    example: 'January 15, 2024'
  },
  {
    name: 'currentYear',
    description: 'Current year',
    example: '2024'
  },
  {
    name: 'unsubscribeUrl',
    description: 'URL for unsubscribing from emails',
    example: 'https://example.com/unsubscribe?token=abc123'
  }
];

// Create a new email template
export async function createEmailTemplate(params: CreateTemplateParams): Promise<number> {
  const now = new Date();
  
  const result = await db.insert(emailTemplates).values({
    name: params.name,
    subject: params.subject,
    body: params.body,
    variables: JSON.stringify(params.variables || []),
    is_system: false,
    created_by: params.createdBy,
    created_at: now,
    updated_at: now,
  });

  return result.insertId;
}

// Update an existing email template
export async function updateEmailTemplate(params: UpdateTemplateParams): Promise<void> {
  const updateData: any = {
    updated_at: new Date(),
  };

  if (params.name !== undefined) updateData.name = params.name;
  if (params.subject !== undefined) updateData.subject = params.subject;
  if (params.body !== undefined) updateData.body = params.body;
  if (params.variables !== undefined) updateData.variables = JSON.stringify(params.variables);

  await db
    .update(emailTemplates)
    .set(updateData)
    .where(eq(emailTemplates.id, params.id));
}

// Get email template by ID
export async function getEmailTemplate(id: number): Promise<EmailTemplate | null> {
  const result = await db
    .select()
    .from(emailTemplates)
    .where(eq(emailTemplates.id, id))
    .limit(1);

  if (result.length === 0) return null;

  const template = result[0];
  return {
    id: template.id,
    name: template.name,
    subject: template.subject,
    body: template.body,
    variables: template.variables ? JSON.parse(template.variables) : [],
    isSystem: template.is_system,
    createdBy: template.created_by,
    createdAt: template.created_at,
    updatedAt: template.updated_at,
  };
}

// Get all email templates with pagination and search
export async function getEmailTemplates(params: {
  page?: number;
  limit?: number;
  search?: string;
  includeSystem?: boolean;
} = {}): Promise<{
  templates: EmailTemplate[];
  total: number;
  page: number;
  totalPages: number;
}> {
  const page = params.page || 1;
  const limit = params.limit || 10;
  const offset = (page - 1) * limit;

  let whereConditions = [];
  
  if (params.search) {
    whereConditions.push(
      or(
        like(emailTemplates.name, `%${params.search}%`),
        like(emailTemplates.subject, `%${params.search}%`)
      )
    );
  }

  if (params.includeSystem === false) {
    whereConditions.push(eq(emailTemplates.is_system, false));
  }

  const whereClause = whereConditions.length > 0 
    ? whereConditions.reduce((acc, condition) => acc && condition)
    : undefined;

  // Get templates
  const templatesQuery = db
    .select({
      id: emailTemplates.id,
      name: emailTemplates.name,
      subject: emailTemplates.subject,
      body: emailTemplates.body,
      variables: emailTemplates.variables,
      is_system: emailTemplates.is_system,
      created_by: emailTemplates.created_by,
      created_at: emailTemplates.created_at,
      updated_at: emailTemplates.updated_at,
      creator_name: users.display_name,
    })
    .from(emailTemplates)
    .leftJoin(users, eq(emailTemplates.created_by, users.ID))
    .orderBy(desc(emailTemplates.created_at))
    .limit(limit)
    .offset(offset);

  if (whereClause) {
    templatesQuery.where(whereClause);
  }

  const templatesResult = await templatesQuery;

  // Get total count
  const countQuery = db
    .select({ count: emailTemplates.id })
    .from(emailTemplates);

  if (whereClause) {
    countQuery.where(whereClause);
  }

  const countResult = await countQuery;
  const total = countResult.length;

  const templates: EmailTemplate[] = templatesResult.map(template => ({
    id: template.id,
    name: template.name,
    subject: template.subject,
    body: template.body,
    variables: template.variables ? JSON.parse(template.variables) : [],
    isSystem: template.is_system,
    createdBy: template.created_by,
    createdAt: template.created_at,
    updatedAt: template.updated_at,
  }));

  return {
    templates,
    total,
    page,
    totalPages: Math.ceil(total / limit),
  };
}

// Delete an email template (only non-system templates)
export async function deleteEmailTemplate(id: number): Promise<void> {
  await db
    .delete(emailTemplates)
    .where(
      eq(emailTemplates.id, id) && eq(emailTemplates.is_system, false)
    );
}

// Process template variables in content
export async function processTemplateVariables(
  content: string,
  variables: Record<string, any> = {}
): Promise<string> {
  let processedContent = content;

  // Get site settings for default variables
  const settings = await getSiteSettings();
  
  // Default variables
  const defaultVars = {
    siteName: settings.siteName,
    siteUrl: settings.siteUrl,
    currentDate: new Date().toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }),
    currentYear: new Date().getFullYear().toString(),
    ...variables
  };

  // Replace variables in the format {{variableName}}
  for (const [key, value] of Object.entries(defaultVars)) {
    const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
    processedContent = processedContent.replace(regex, String(value || ''));
  }

  return processedContent;
}

// Generate unsubscribe URL for a user
export function generateUnsubscribeUrl(token: string): string {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  return `${baseUrl}/unsubscribe?token=${token}`;
}

// Validate template content for required variables
export function validateTemplateContent(content: string): {
  isValid: boolean;
  missingVariables: string[];
  invalidVariables: string[];
} {
  const variableRegex = /{{(\s*\w+\s*)}}/g;
  const foundVariables: string[] = [];
  let match;

  while ((match = variableRegex.exec(content)) !== null) {
    const variableName = match[1].trim();
    foundVariables.push(variableName);
  }

  const validVariableNames = DEFAULT_TEMPLATE_VARIABLES.map(v => v.name);
  const invalidVariables = foundVariables.filter(v => !validVariableNames.includes(v));
  
  // For now, we don't require any specific variables, so no missing variables
  const missingVariables: string[] = [];

  return {
    isValid: invalidVariables.length === 0,
    missingVariables,
    invalidVariables,
  };
}

// Preview template with sample data
export async function previewTemplate(templateId: number, sampleData: Record<string, any> = {}): Promise<{
  subject: string;
  body: string;
}> {
  const template = await getEmailTemplate(templateId);
  if (!template) {
    throw new Error('Template not found');
  }

  // Sample data for preview
  const previewData = {
    userName: 'John Doe',
    userEmail: '<EMAIL>',
    ...sampleData
  };

  const processedSubject = await processTemplateVariables(template.subject, previewData);
  const processedBody = await processTemplateVariables(template.body, previewData);

  return {
    subject: processedSubject,
    body: processedBody,
  };
}
